/**
 * UI界面管理器
 *
 * 功能说明：
 * 1. 管理游戏中所有UI界面的生命周期（打开、关闭、切换）
 * 2. 处理UI资源的加载和释放
 * 3. 管理UI界面的层级关系和显示顺序
 * 4. 提供UI配置注册和查询功能
 * 5. 支持UI界面的队列化打开机制
 *
 * 设计模式：
 * - 单例模式：全局唯一的UI管理器实例
 * - 工厂模式：根据配置动态创建UI界面
 * - 队列模式：管理UI打开的先后顺序
 *
 * 核心特性：
 * - 支持Bundle分包加载
 * - 自动管理UI层级
 * - 支持模态和非模态界面
 * - 提供UI缓存机制
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_awaiter = __awaiter;
const cc_generator = __generator;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

// 依赖模块导入
const LogManager = require("LogManager");
const SingletonManager = require("SingletonManager");
const ResourceKeeper = require("ResKeeper");
const ResourceLoader = require("ResLoader");
const ResourceUtil = require("ResUtil");
const UIView = require("UIView");

/**
 * UI管理器类
 *
 * 继承自单例管理器，确保全局唯一性
 */
const UIManagerClass = function (BaseSingleton) {

  /**
   * 构造函数
   * 初始化UI管理器的核心数据结构
   */
  function UIManager() {
    const instance = BaseSingleton !== null && BaseSingleton.apply(this, arguments) || this;

    /** 是否正在打开界面的标志位，用于防止并发打开 */
    instance._isOpening = false;

    /** UI打开队列，存储等待打开的UI请求 */
    instance._openQueue = [];

    /** UI配置映射表，存储Bundle名称到UI配置的映射 */
    instance._uiConfigMap = new Map();

    /** 当前打开的UI列表，存储所有已打开的UI实例 */
    instance._activeUIList = new Map();

    return instance;
  }

  // 设置继承关系
  cc_extends(UIManager, BaseSingleton);

  /**
   * 打开UI界面
   *
   * @param {string} bundleName - Bundle包名称
   * @param {string} uiName - UI界面名称
   * @param {...any} args - 传递给UI的参数
   *
   * 执行流程：
   * 1. 验证UI配置是否存在
   * 2. 检查是否正在打开其他界面
   * 3. 如果界面已存在则恢复显示
   * 4. 否则创建新的UI实例
   */
  UIManager.prototype.open = function (bundleName, uiName) {
    const self = this;

    // 提取传递给UI的参数
    const uiArgs = [];
    for (let i = 2; i < arguments.length; i++) {
      uiArgs[i - 2] = arguments[i];
    }

    // 获取Bundle的UI配置
    const bundleConfig = this._uiConfigMap.get(bundleName);
    if (!bundleConfig) {
      LogManager.LogMgr.warn(`Bundle: ${bundleName} 未注册UI配置`);
      return;
    }

    // 获取具体UI的配置
    const uiConfig = bundleConfig[uiName];
    if (!uiConfig) {
      LogManager.LogMgr.warn(`Bundle: ${bundleName} UI: ${uiName} 未注册UI配置`);
      return;
    }

    // 将参数附加到UI配置中
    uiConfig.args = uiArgs;

    // 检查是否正在打开其他界面
    if (this._isOpening) {
      LogManager.LogMgr.warn(`正在打开其他界面，${uiName} 加入等待队列`);

      // 创建UI打开请求并加入队列
      const openRequest = {
        bundleName: bundleName,
        uiName: uiName,
        uiInfo: uiConfig
      };
      this._openQueue.push(openRequest);
      return;
    }

    // 检查UI是否已经打开
    const uiKey = this._generateUIKey(bundleName, uiName);
    const existingUI = this._activeUIList.get(uiKey);

    if (cc.isValid(existingUI, true)) {
      // UI已存在，关闭层级更高的UI并恢复当前UI
      const currentZIndex = existingUI.node.zIndex;
      this._activeUIList.forEach(function (ui) {
        if (ui.node.zIndex > currentZIndex) {
          self.close(ui);
        }
      });

      // 恢复UI显示并传递参数
      existingUI.onResume.apply(existingUI, uiArgs);
      return;
    }

    // 创建新的UI实例
    this._showUI(bundleName, uiName, uiConfig);
  };
  /**
   * 关闭UI界面
   *
   * @param {UIView} uiView - 要关闭的UI视图实例
   *
   * 执行流程：
   * 1. 调用UI的生命周期方法
   * 2. 清理UI的事件监听
   * 3. 从活动列表中移除
   * 4. 销毁UI节点和资源
   */
  UIManager.prototype.close = function (uiView) {
    if (!uiView) {
      LogManager.LogMgr.warn("尝试关闭空的UI视图");
      return;
    }

    const bundleName = uiView.bundleName;
    const uiName = uiView.uiName;

    // 调用UI生命周期方法
    uiView.showHideViews();  // 隐藏子视图
    uiView.rmAllEvents();    // 移除所有事件监听
    uiView.onClose();        // 调用关闭回调

    // 从活动UI列表中移除
    const uiKey = this._generateUIKey(bundleName, uiName);
    this._activeUIList.delete(uiKey);

    // 如果UI不需要缓存，则销毁相关资源
    if (!uiView.isCache) {
      const resourceKeepers = uiView.node.getComponentsInChildren(ResourceKeeper.default);
      for (let i = 0; i < resourceKeepers.length; i++) {
        resourceKeepers[i].node.destroy();
      }
    }

    // 从场景中移除UI节点
    uiView.node.removeFromParent(true);
  };

  /**
   * 通过Bundle名称和UI名称关闭界面
   *
   * @param {string} bundleName - Bundle包名称
   * @param {string} uiName - UI界面名称
   */
  UIManager.prototype.closeView = function (bundleName, uiName) {
    const uiKey = this._generateUIKey(bundleName, uiName);
    const uiView = this._activeUIList.get(uiKey);

    if (uiView) {
      this.close(uiView);
    } else {
      LogManager.LogMgr.warn(`UIManager关闭界面失败: bundle: ${bundleName}, view: ${uiName} 不存在`);
    }
  };

  /**
   * 关闭所有打开的UI界面
   */
  UIManager.prototype.closeAll = function () {
    const self = this;
    this._activeUIList.forEach(function (uiView) {
      self.close(uiView);
    });
  };

  /**
   * 注册Bundle的UI配置
   *
   * @param {string} bundleName - Bundle包名称
   * @param {Object} uiConfig - UI配置对象
   */
  UIManager.prototype.register = function (bundleName, uiConfig) {
    if (this._uiConfigMap.has(bundleName)) {
      LogManager.LogMgr.warn(`Bundle: ${bundleName} 重复注册UI配置`);
    }
    this._uiConfigMap.set(bundleName, uiConfig);
  };

  /**
   * 取消注册Bundle的UI配置
   *
   * @param {string} bundleName - Bundle包名称
   */
  UIManager.prototype.unRegister = function (bundleName) {
    this._uiConfigMap.delete(bundleName);
  };

  /**
   * 清空所有UI配置
   */
  UIManager.prototype.unRegisterAll = function () {
    this._uiConfigMap.clear();
  };

  /**
   * 根据名称获取UI视图实例
   *
   * @param {string} bundleName - Bundle包名称
   * @param {string} uiName - UI界面名称
   * @returns {UIView|undefined} UI视图实例
   */
  UIManager.prototype.getViewByName = function (bundleName, uiName) {
    const uiKey = this._generateUIKey(bundleName, uiName);
    return this._activeUIList.get(uiKey);
  };

  /**
   * 获取所有活动的UI视图
   *
   * @returns {UIView[]} 所有活动UI视图的数组
   */
  UIManager.prototype.getAllViews = function () {
    return Array.from(this._activeUIList.values());
  };

  /**
   * 生成UI的唯一标识符
   *
   * @param {string} bundleName - Bundle包名称
   * @param {string} uiName - UI界面名称
   * @returns {string} UI唯一标识符
   * @private
   */
  UIManager.prototype._generateUIKey = function (bundleName, uiName) {
    return bundleName + "_" + uiName;
  };
  /**
   * 显示UI界面的核心方法
   *
   * @param {string} bundleName - Bundle包名称
   * @param {string} uiName - UI界面名称
   * @param {Object} uiConfig - UI配置对象
   * @returns {Promise} 异步操作Promise
   * @private
   *
   * 执行流程：
   * 1. 设置正在打开标志
   * 2. 创建阻塞输入节点（如果需要）
   * 3. 加载Bundle资源
   * 4. 加载UI预制体
   * 5. 实例化UI并初始化
   */
  UIManager.prototype._showUI = function (bundleName, uiName, uiConfig) {
    return cc_awaiter(this, undefined, undefined, function () {
      let blockInputNode;
      let maskSprite;
      let bundle;
      let prefabPath;
      let prefabAsset;
      let uiRoot;
      let uiNode;
      let uiView;

      return cc_generator(this, function (step) {
        switch (step.label) {
          case 0:
            // 设置正在打开UI的标志
            this._isOpening = true;
            blockInputNode = null;

            // 如果需要阻塞输入，创建阻塞节点
            if (uiConfig.block) {
              blockInputNode = this._createBlockInputNode(bundleName, uiName);

              // 如果需要遮罩效果
              if (uiConfig.mask) {
                maskSprite = blockInputNode.addComponent(cc.Sprite);
                maskSprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;

                // 加载遮罩纹理
                ResourceLoader.default.loadRes("resources", "texture/res/singleColor", cc.SpriteFrame).then(function (spriteFrame) {
                  maskSprite.spriteFrame = spriteFrame;
                });

                // 设置遮罩颜色和透明度
                blockInputNode.color = cc.color(0, 0, 0);
                blockInputNode.opacity = 180;
              }
            }

            // 加载Bundle资源
            return [4, ResourceLoader.default.loadBundle(bundleName)];

          case 1:
            bundle = step.sent();
            if (bundle) {
              prefabPath = uiConfig.prefab;
              return [4, ResourceLoader.default.loadRes(bundle, prefabPath, cc.Prefab)];
            } else {
              LogManager.LogMgr.error(`无法找到Bundle: ${bundleName}`);
              return [2, this._executeNextUIInQueue(blockInputNode)];
            }

          case 2:
            prefabAsset = step.sent();
            if (prefabAsset) {
              // 获取UI根节点并实例化预制体
              uiRoot = this.getUIRoot();
              uiNode = ResourceUtil.ResUtil.instantiate(prefabAsset);
              uiView = uiNode.getComponent(UIView.default);

              // 添加到UI根节点
              uiRoot.addChild(uiNode);

              // 设置UI层级和名称
              uiNode.zIndex = this._activeUIList.size + 1;
              uiNode.name = this._generateUIKey(bundleName, uiName);

              if (uiView) {
                // 处理阻塞节点
                this._executeNextUIInQueue(blockInputNode, uiNode);

                // 初始化UI视图
                uiView.init(bundleName, uiName, uiConfig.args);

                // 添加到活动UI列表
                const uiKey = this._generateUIKey(bundleName, uiName);
                this._activeUIList.set(uiKey, uiView);

                LogManager.LogMgr.debug(`UIManager成功打开界面: ${uiName}`);
                return [2];
              } else {
                LogManager.LogMgr.warn(`UI: ${uiName} 未注册UIView组件`);
                return [2, this._executeNextUIInQueue(blockInputNode)];
              }
            } else {
              LogManager.LogMgr.error(`无法找到Bundle: ${bundleName} 的预制体: ${prefabPath}`);
              return [2, this._executeNextUIInQueue(blockInputNode)];
            }
        }
      });
    });
  };
  /**
   * 执行队列中的下一个UI打开请求
   *
   * @param {cc.Node} blockInputNode - 阻塞输入节点
   * @param {cc.Node} uiNode - UI节点
   * @private
   */
  UIManager.prototype._executeNextUIInQueue = function (blockInputNode, uiNode) {
    // 清除正在打开标志
    this._isOpening = false;

    // 处理阻塞输入节点
    if (uiNode && blockInputNode) {
      // 将阻塞节点作为UI的子节点
      blockInputNode.parent = uiNode;
      blockInputNode.zIndex = -1;
      blockInputNode.setPosition(cc.v2(0, 0));
    } else if (blockInputNode) {
      // 移除阻塞节点
      blockInputNode.removeFromParent();
    }

    // 处理队列中的下一个UI打开请求
    if (this._openQueue.length > 0) {
      const nextRequest = this._openQueue.shift();
      this._showUI(nextRequest.bundleName, nextRequest.uiName, nextRequest.uiInfo);
    }
  };

  /**
   * 创建阻塞输入的节点
   *
   * @param {string} bundleName - Bundle包名称
   * @param {string} uiName - UI界面名称
   * @returns {cc.Node} 阻塞输入节点
   * @private
   */
  UIManager.prototype._createBlockInputNode = function (bundleName, uiName) {
    const blockNode = new cc.Node(`${bundleName}_${uiName}_block`);

    // 设置节点大小为屏幕大小
    blockNode.setContentSize(cc.winSize);

    // 添加阻塞输入组件
    blockNode.addComponent(cc.BlockInputEvents);

    // 添加到UI阻塞层
    this.getUIBlock().addChild(blockNode);

    // 设置最高层级
    blockNode.zIndex = 999;

    return blockNode;
  };

  // ==================== UI层级节点获取方法 ====================

  /**
   * 获取UI根节点
   *
   * @returns {cc.Node} UI根节点
   */
  UIManager.prototype.getUIRoot = function () {
    return cc.director.getScene().getChildByName("Canvas").getChildByName("UIRoot");
  };

  /**
   * 获取UI阻塞层节点
   *
   * @returns {cc.Node} UI阻塞层节点
   */
  UIManager.prototype.getUIBlock = function () {
    return cc.director.getScene().getChildByName("Canvas").getChildByName("UIBlock");
  };

  /**
   * 获取UI提示层节点
   *
   * @returns {cc.Node} UI提示层节点
   */
  UIManager.prototype.getUITips = function () {
    return cc.director.getScene().getChildByName("Canvas").getChildByName("UITips");
  };

  /**
   * 获取UI加载层节点
   *
   * @returns {cc.Node} UI加载层节点
   */
  UIManager.prototype.getUILoading = function () {
    return cc.director.getScene().getChildByName("Canvas").getChildByName("UILoading");
  };

  /**
   * 获取UI子游戏层节点
   *
   * @returns {cc.Node} UI子游戏层节点
   */
  UIManager.prototype.getUISubgame = function () {
    return cc.director.getScene().getChildByName("Canvas").getChildByName("UISubgame");
  };

  /**
   * 获取游戏相机组件
   *
   * @returns {cc.Camera|null} 游戏相机组件
   */
  UIManager.prototype.getGameCamera = function () {
    let camera = null;
    const cameraNode = cc.director.getScene().getChildByName("Canvas").getChildByName("Game_Camera");

    if (cameraNode) {
      camera = cameraNode.getComponent(cc.Camera);
    }

    return camera;
  };

  // 返回UI管理器类
  return UIManager;

}(SingletonManager.SingletonMgr);

// 导出UI管理器单例实例
exports.default = UIManagerClass.getInstance();