/**
 * 子弹工厂类
 *
 * 功能说明：
 * 1. 负责创建和管理游戏中所有类型的子弹对象
 * 2. 使用对象池模式优化子弹的创建和回收性能
 * 3. 根据武器配置动态加载对应的子弹预制体
 * 4. 提供子弹的获取、回收和池管理功能
 *
 * 设计模式：
 * - 工厂模式：统一创建不同类型的子弹对象
 * - 对象池模式：复用子弹对象，减少内存分配开销
 * - 单例模式：全局唯一的子弹工厂实例
 *
 * 性能优化：
 * - 预注册常用子弹类型到对象池
 * - 异步加载子弹预制体资源
 * - 批量回收子弹对象
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_decorate = __decorate;
const cc_awaiter = __awaiter;
const cc_generator = __generator;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

// 依赖模块导入
const NodePoolManager = require("NodePoolManager");
const ResourceLoader = require("ResLoader");
const ExcelData = require("Excel");
const BaseFactory = require("BaseFactory");

// Cocos Creator 装饰器
const { ccclass, property, inspector } = cc._decorator;

/**
 * 子弹工厂类
 *
 * 继承自BaseFactory，提供子弹对象的创建、管理和回收功能
 */
const BulletFactoryClass = function (BaseFactoryClass) {

  /**
   * 构造函数
   * 初始化子弹工厂
   */
  function BulletFactory() {
    return BaseFactoryClass !== null && BaseFactoryClass.apply(this, arguments) || this;
  }

  // 设置继承关系
  cc_extends(BulletFactory, BaseFactoryClass);

  /**
   * 注册子弹对象池
   *
   * @param {string} bulletName - 子弹预制体名称
   * @returns {Promise<void>} 注册完成的Promise
   *
   * 执行流程：
   * 1. 异步加载子弹预制体资源
   * 2. 将预制体注册到对象池管理器
   * 3. 记录已注册的预制体名称
   */
  BulletFactory.prototype.registerBulletPool = function (bulletName) {
    return cc_awaiter(this, undefined, undefined, function () {
      let bulletPrefab;

      return cc_generator(this, function (step) {
        switch (step.label) {
          case 0:
            // 验证参数
            if (!bulletName) {
              console.error("BulletFactory: 子弹名称不能为空");
              return [2];
            }

            // 异步加载子弹预制体
            return [4, ResourceLoader.default.loadRes("game", `prefabs/bullets/${bulletName}`, cc.Prefab)];

          case 1:
            bulletPrefab = step.sent();

            if (!bulletPrefab) {
              console.error(`BulletFactory: 无法加载子弹预制体 ${bulletName}`);
              return [2];
            }

            // 注册到对象池（初始数量为1，组件类型为BaseBullet）
            NodePoolManager.NodePoolMgr.register(bulletName, bulletPrefab, 1, "BaseBullet");

            // 记录已注册的预制体
            this._prefab.push(bulletName);

            console.log(`BulletFactory: 成功注册子弹池 ${bulletName}`);
            return [2];
        }
      });
    });
  };

  /**
   * 获取子弹对象
   *
   * @param {number} weaponId - 武器ID，用于查找对应的子弹配置
   * @returns {Promise<cc.Node|null>} 子弹节点对象或null
   *
   * 执行流程：
   * 1. 根据武器ID查找子弹配置数据
   * 2. 检查子弹对象池是否已注册
   * 3. 如未注册则先注册对象池
   * 4. 从对象池获取子弹对象并初始化
   */
  BulletFactory.prototype.getBullet = function (weaponId) {
    return cc_awaiter(this, undefined, undefined, function () {
      let bulletConfig;
      let bulletNode;
      let bulletName;

      return cc_generator(this, function (step) {
        switch (step.label) {
          case 0:
            // 参数验证
            if (weaponId === undefined || weaponId === null) {
              console.error("BulletFactory: 武器ID不能为空");
              return [2, null];
            }

            // 根据武器ID获取子弹配置
            bulletConfig = ExcelData.Excel.haizhanwuqibullet(weaponId);
            bulletNode = null;

            if (!bulletConfig) {
              console.error(`BulletFactory: 未找到武器 ${weaponId} 对应的子弹配置数据`);
              return [2, null];
            }

            // 获取子弹预制体名称
            bulletName = bulletConfig.bulletname;
            if (!bulletName) {
              console.error(`BulletFactory: 武器 ${weaponId} 的子弹配置中缺少bulletname字段`);
              return [2, null];
            }

            // 检查对象池是否已注册
            if (NodePoolManager.NodePoolMgr.has(bulletName)) {
              // 对象池已存在，直接获取
              return [3, 2];
            } else {
              // 对象池不存在，先注册
              return [4, this.registerBulletPool(bulletName)];
            }

          case 1:
            // 注册完成
            step.sent();
            step.label = 2;

          case 2:
            // 从对象池获取子弹对象
            bulletNode = NodePoolManager.NodePoolMgr.get(bulletName);

            if (bulletNode) {
              // 重置子弹状态
              bulletNode.scale = 1;
              bulletNode.active = true;

              console.log(`BulletFactory: 成功获取子弹 ${bulletName}`);
            } else {
              console.warn(`BulletFactory: 从对象池获取子弹 ${bulletName} 失败`);
            }

            return [2, bulletNode];

          default:
            console.error(`BulletFactory: getBullet 执行异常，武器ID: ${weaponId}`);
            return [2, null];
        }
      });
    });
  };

  /**
   * 回收子弹对象到对象池
   *
   * @param {cc.Node|cc.Node[]} bulletNodes - 要回收的子弹节点（单个或数组）
   *
   * 功能说明：
   * 1. 支持单个子弹或批量子弹回收
   * 2. 根据节点名称将子弹放回对应的对象池
   * 3. 提供错误处理和日志记录
   */
  BulletFactory.prototype.putBullet = function (bulletNodes) {
    // 确保输入为数组格式
    const nodeArray = [].concat(bulletNodes);

    if (!nodeArray || nodeArray.length === 0) {
      console.warn("BulletFactory: 没有需要回收的子弹节点");
      return;
    }

    // 批量回收子弹
    for (let i = 0; i < nodeArray.length; i++) {
      const bulletNode = nodeArray[i];

      if (!bulletNode || !bulletNode.name) {
        console.warn(`BulletFactory: 第 ${i} 个子弹节点无效，跳过回收`);
        continue;
      }

      try {
        // 重置节点状态
        bulletNode.active = false;
        bulletNode.scale = 1;

        // 放回对象池
        const bulletName = bulletNode.name;
        NodePoolManager.NodePoolMgr.put(bulletName, bulletNode);

        console.log(`BulletFactory: 成功回收子弹 ${bulletName}`);
      } catch (error) {
        console.error(`BulletFactory: 回收子弹 ${bulletNode.name} 时出错`, error);
      }
    }
  };

  /**
   * 获取对象池统计信息
   *
   * @returns {Object} 包含各种子弹池状态的统计信息
   */
  BulletFactory.prototype.getPoolStats = function () {
    const stats = {
      registeredPools: this._prefab.length,
      poolNames: [...this._prefab]
    };

    return stats;
  };

  /**
   * 清理所有子弹对象池
   *
   * 用于场景切换或游戏重置时清理资源
   */
  BulletFactory.prototype.clearAllPools = function () {
    try {
      for (const bulletName of this._prefab) {
        NodePoolManager.NodePoolMgr.clear(bulletName);
      }

      this._prefab.length = 0;
      console.log("BulletFactory: 已清理所有子弹对象池");
    } catch (error) {
      console.error("BulletFactory: 清理对象池时出错", error);
    }
  };

  // 应用Cocos Creator装饰器
  return cc_decorate([ccclass], BulletFactory);

}(BaseFactory.default);

// 导出子弹工厂单例实例
exports.default = BulletFactoryClass.getInstance();