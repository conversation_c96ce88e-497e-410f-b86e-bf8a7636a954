/**
 * 游戏主入口控制器
 *
 * 功能说明：
 * 1. 负责游戏启动时的初始化流程
 * 2. 预加载通用UI预制体资源
 * 3. 管理加载界面的显示和隐藏
 * 4. 处理游戏主循环的调度更新
 *
 * 设计模式：
 * - 继承自UIScene，作为场景控制器
 * - 使用事件监听模式处理加载状态变化
 * - 采用资源预加载策略优化启动性能
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// Cocos Creator 装饰器和继承工具
const cc_extends = __extends;
const cc_decorate = __decorate;

// 导出模块定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

// 依赖模块导入 - 使用更清晰的命名
const EntryUIConfig = require("UIConfig_Entry");
const LogManager = require("LogManager");
const ScheduleManager = require("ScheduleManage");
const UIManager = require("UIManager");
const UIScene = require("UIScene");
const EventManager = require("EventManager");
const EventTypes = require("EvenType");
const LoadingManager = require("LoadingManager");

// Cocos Creator 装饰器
const { ccclass, property, inspector } = cc._decorator;

/**
 * 游戏入口场景控制器类
 *
 * 职责：
 * - 管理游戏启动流程
 * - 预加载核心UI资源
 * - 协调各个管理器的初始化
 */
const GameEntryController = function (BaseUIScene) {

  /**
   * 构造函数
   * 继承自UIScene基类
   */
  function EntryController() {
    return BaseUIScene !== null && BaseUIScene.apply(this, arguments) || this;
  }

  // 设置继承关系
  cc_extends(EntryController, BaseUIScene);

  /**
   * 场景显示时的初始化方法
   *
   * 执行流程：
   * 1. 收集需要预加载的UI预制体路径
   * 2. 执行资源预加载
   * 3. 预加载完成后打开入口UI
   * 4. 注册加载状态变化事件监听
   */
  EntryController.prototype._show = function () {
    // 收集所有入口UI配置中的预制体路径
    const prefabPaths = [];
    for (const uiKey in EntryUIConfig.UIConfig_Entry) {
      const uiConfig = EntryUIConfig.UIConfig_Entry[uiKey];
      if (uiConfig && uiConfig.prefab) {
        prefabPaths.push(uiConfig.prefab);
      }
    }

    // 预加载通用UI预制体
    cc.resources.preload(prefabPaths, (error) => {
      if (error) {
        LogManager.LogMgr.error("预加载通用预制体失败: " + error);
      }

      // 预加载完成后打开入口UI界面
      UIManager.default.open("resources", EntryUIConfig.UIView_Entry.UI_Entry);
    });

    // 注册加载视图状态变化事件监听
    EventManager.EventMgr.addEventListener(
      EventTypes.EVENT_TYPE.Game_Load_View,
      this.onViewLoadStateChange,
      this
    );
  };

  /**
   * 处理视图加载状态变化
   *
   * @param {boolean} isLoading - 是否正在加载
   * @param {string} loadingText - 加载提示文本
   */
  EntryController.prototype.onViewLoadStateChange = function (isLoading, loadingText) {
    if (isLoading) {
      // 显示加载界面
      LoadingManager.LoadingMgr.show(loadingText);
    } else {
      // 隐藏加载界面
      LoadingManager.LoadingMgr.hide();
    }
  };

  /**
   * 游戏主循环更新方法
   *
   * @param {number} deltaTime - 帧间隔时间
   */
  EntryController.prototype.update = function (deltaTime) {
    // 更新调度管理器，处理定时任务和延迟调用
    ScheduleManager.ScheduleMgr.update(deltaTime);
  };

  // 应用Cocos Creator装饰器
  return cc_decorate([
    ccclass,
    inspector("packages://base-ui/dist/inspector.js")
  ], EntryController);

}(UIScene.default);

// 导出入口控制器类
exports.default = GameEntryController;