/**
 * 音频管理器
 *
 * 功能说明：
 * 1. 管理游戏中的背景音乐播放和控制
 * 2. 管理游戏音效的播放和音量控制
 * 3. 管理设备震动功能的开关
 * 4. 管理辅助线显示功能的开关
 * 5. 提供音频设置的持久化存储
 *
 * 核心系统：
 * - 音乐系统：背景音乐的播放、暂停、音量控制
 * - 音效系统：游戏音效的播放、快速播放、音量控制
 * - 设置系统：音频相关设置的保存和加载
 * - 震动系统：设备震动功能的控制
 * - 辅助功能：游戏辅助线等功能的控制
 *
 * 设计模式：
 * - 单例模式：全局唯一的音频管理器实例
 * - 策略模式：不同音频类型的差异化处理
 * - 观察者模式：设置变化时的事件通知
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_assign = __assign;
const cc_awaiter = __awaiter;
const cc_generator = __generator;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.AudioMgr = undefined;

// 依赖模块导入
const $9EvenType = require("EvenType");
const $9ResLoader = require("ResLoader");
const $9DataManager = require("DataManager");
const $9EventManager = require("EventManager");
const $9LogManager = require("LogManager");

/**
 * 音频设置数据接口
 * 定义音频管理器的所有设置项
 */
const AudioSettingsInterface = {
  /** 是否显示辅助线 */
  fuZhuXian: true,

  /** 音乐音量（0-1） */
  musicVolume: 0.5,

  /** 是否开启音乐 */
  musicOn: true,

  /** 音效音量（0-1） */
  effectVolume: 1.0,

  /** 是否开启音效 */
  effectOn: true,

  /** 是否开启震动 */
  shakeOn: true
};

/**
 * 音频管理器类
 *
 * 继承自DataManager基类，提供完整的音频管理功能
 */
const AudioManagerClass = function (BaseDataManager) {

  /**
   * 构造函数
   * 初始化音频管理器的所有属性
   */
  function AudioManager() {
    const instance = BaseDataManager !== null && BaseDataManager.apply(this, arguments) || this;

    // ==================== 基础配置 ====================
    /** 数据存储密钥 */
    instance._secret_key = "audio";

    // ==================== 音频设置 ====================
    /** 音频设置数据 */
    instance._setting = null;

    // ==================== 音乐系统 ====================
    /** 当前播放的音乐标识 */
    instance._curMusic = null;

    // ==================== 音效系统 ====================
    /** 音效数据缓存（用于快速播放） */
    instance.audioData = {};

    return instance;
  }

  // 设置继承关系
  cc_extends(AudioManager, BaseDataManager);
  // ==================== 音频设置属性访问器 ====================

  /**
   * 音乐开关状态访问器
   * 如果设置未初始化，默认返回true（开启状态）
   */
  Object.defineProperty(AudioManager.prototype, "musicOn", {
    get: function () {
      return !this._setting || this._setting.musicOn;
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 音效开关状态访问器
   * 如果设置未初始化，默认返回true（开启状态）
   */
  Object.defineProperty(AudioManager.prototype, "effectOn", {
    get: function () {
      return !this._setting || this._setting.effectOn;
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 震动开关状态访问器
   * 如果设置未初始化，默认返回true（开启状态）
   */
  Object.defineProperty(AudioManager.prototype, "shakeOn", {
    get: function () {
      return !this._setting || this._setting.shakeOn;
    },
    enumerable: false,
    configurable: true
  });
  // ==================== 初始化方法 ====================

  /**
   * 初始化音频管理器
   *
   * @returns {Promise<void>} 初始化完成的Promise
   *
   * 执行流程：
   * 1. 异步加载音频设置数据
   * 2. 合并默认设置和已保存的设置
   * 3. 完成初始化回调
   */
  AudioManager.prototype.init = function () {
    const self = this;

    return new Promise(function (resolve) {
      return cc_awaiter(self, undefined, undefined, function () {
        let savedSettings;
        let defaultSettings;

        return cc_generator(this, function (step) {
          switch (step.label) {
            case 0:
              // 异步加载已保存的设置
              return [4, this._load("setting")];

            case 1:
              savedSettings = step.sent();

              // 定义默认设置
              defaultSettings = {
                fuZhuXian: true,
                musicVolume: 0.5,
                musicOn: true,
                effectVolume: 1.0,
                effectOn: true,
                shakeOn: true
              };

              // 合并默认设置和已保存的设置
              this._setting = cc_assign(cc_assign({}, defaultSettings), savedSettings);

              // 应用音频设置
              this._applyAudioSettings();

              // 完成初始化
              resolve();

              console.log("AudioManager: 初始化完成", this._setting);
              return [2];
          }
        });
      });
    });
  };

  /**
   * 应用音频设置到引擎
   * @private
   */
  AudioManager.prototype._applyAudioSettings = function () {
    try {
      // 应用音乐音量设置
      if (this._setting.musicVolume !== undefined) {
        cc.audioEngine.setMusicVolume(this._setting.musicVolume);
      }

      // 应用音效音量设置
      if (this._setting.effectVolume !== undefined) {
        cc.audioEngine.setEffectsVolume(this._setting.effectVolume);
      }

      console.log("AudioManager: 音频设置已应用");
    } catch (error) {
      console.error("AudioManager: 应用音频设置时出错", error);
    }
  };
  // ==================== 音乐系统方法 ====================

  /**
   * 播放背景音乐
   *
   * @param {string} musicName - 音乐文件名
   * @param {boolean} loop - 是否循环播放（默认true）
   * @param {string} bundleName - 资源包名称（默认"audio"）
   *
   * 功能说明：
   * 1. 检查是否为新的音乐文件
   * 2. 验证音乐开关状态
   * 3. 异步加载音乐资源
   * 4. 播放音乐并保存当前状态
   */
  AudioManager.prototype.playMusic = function (musicName, loop, bundleName) {
    // 设置默认参数
    if (loop === undefined) {
      loop = true;
    }
    if (bundleName === undefined) {
      bundleName = "audio";
    }

    // 参数验证
    if (!musicName) {
      console.warn("AudioManager: 音乐文件名不能为空");
      return;
    }

    // 生成音乐标识
    const musicId = bundleName + "&" + musicName;

    // 检查是否为当前正在播放的音乐
    if (musicId === this._curMusic) {
      return;
    }

    // 更新当前音乐标识
    this._curMusic = musicId;

    // 检查音乐开关状态
    if (this._setting && this._setting.musicOn) {
      // 保存设置
      this._save("setting", this._setting);

      // 异步加载并播放音乐
      $9ResLoader.default.loadRes(bundleName, musicName, cc.AudioClip).then(function (audioClip) {
        if (audioClip) {
          cc.audioEngine.playMusic(audioClip, loop);
          console.log(`AudioManager: 播放音乐 ${musicName}`);
        } else {
          console.error(`AudioManager: 播放音乐失败 - ${bundleName}/${musicName}`);
          $9LogManager.LogMgr.debug("AudioManager playBgm failed", bundleName, musicName);
        }
      }).catch(function (error) {
        console.error("AudioManager: 加载音乐资源时出错", error);
      });
    }
  };

  /**
   * 停止背景音乐
   */
  AudioManager.prototype.stopMusic = function () {
    cc.audioEngine.stopMusic();
    this._curMusic = null;
    console.log("AudioManager: 停止音乐");
  };

  /**
   * 恢复背景音乐播放
   */
  AudioManager.prototype.resumeMusic = function () {
    cc.audioEngine.resumeMusic();
    console.log("AudioManager: 恢复音乐");
  };

  /**
   * 切换音乐开关状态
   *
   * @param {boolean} enabled - 是否开启音乐
   */
  AudioManager.prototype.switchMusic = function (enabled) {
    if (this._setting) {
      this._setting.musicOn = enabled;

      if (enabled) {
        cc.audioEngine.resumeMusic();
      } else {
        cc.audioEngine.pauseMusic();
      }

      this._save("setting", this._setting);
      console.log(`AudioManager: 音乐开关 ${enabled ? '开启' : '关闭'}`);
    }
  };

  /**
   * 设置音乐音量
   *
   * @param {number} volume - 音量值（0-1）
   */
  AudioManager.prototype.setMusicVolume = function (volume) {
    // 参数验证
    if (volume < 0 || volume > 1) {
      console.warn("AudioManager: 音量值应在0-1之间", volume);
      volume = Math.max(0, Math.min(1, volume));
    }

    if (this._setting) {
      this._setting.musicVolume = volume;
      cc.audioEngine.setMusicVolume(volume);
      this._setting.musicOn = volume > 0;

      this._save("setting", this._setting);
      console.log(`AudioManager: 设置音乐音量 ${volume}`);
    }
  };
  // ==================== 音效系统方法 ====================

  /**
   * 播放音效
   *
   * @param {string} effectName - 音效文件名
   * @param {boolean} loop - 是否循环播放（默认false）
   * @param {string} bundleName - 资源包名称（默认"audio"）
   * @returns {Promise<number>} 音效ID的Promise，-1表示播放失败
   *
   * 功能说明：
   * 1. 检查音效开关状态
   * 2. 异步加载音效资源
   * 3. 播放音效并返回音效ID
   */
  AudioManager.prototype.playEffect = function (effectName, loop, bundleName) {
    // 设置默认参数
    if (loop === undefined) {
      loop = false;
    }
    if (bundleName === undefined) {
      bundleName = "audio";
    }

    // 参数验证
    if (!effectName) {
      console.warn("AudioManager: 音效文件名不能为空");
      return Promise.resolve(-1);
    }

    return cc_awaiter(this, undefined, undefined, function () {
      let audioId;

      return cc_generator(this, function () {
        audioId = -1;

        // 检查音效开关状态
        if (this._setting && this._setting.effectOn) {
          return [2, new Promise(function (resolve) {
            $9ResLoader.default.loadRes(bundleName, effectName, cc.AudioClip).then(function (audioClip) {
              if (audioClip) {
                audioId = cc.audioEngine.playEffect(audioClip, loop);
                console.log(`AudioManager: 播放音效 ${effectName}, ID: ${audioId}`);
              } else {
                console.error(`AudioManager: 播放音效失败 - ${bundleName}/${effectName}`);
                $9LogManager.LogMgr.debug("AudioManager playEffect failed", bundleName, effectName);
              }
              resolve(audioId);
            }).catch(function (error) {
              console.error("AudioManager: 加载音效资源时出错", error);
              resolve(-1);
            });
          })];
        } else {
          return [2, audioId];
        }
      });
    });
  };
  /**
   * 快速播放音效（会停止同名音效的之前播放）
   *
   * @param {string} effectName - 音效文件名
   * @param {boolean} loop - 是否循环播放（默认false）
   * @param {string} bundleName - 资源包名称（默认"audio"）
   * @returns {Promise<number>} 音效ID的Promise，-1表示播放失败
   *
   * 功能说明：
   * 1. 如果同名音效正在播放，先停止之前的播放
   * 2. 播放新的音效并缓存音效ID
   * 3. 适用于需要快速重复播放的音效
   */
  AudioManager.prototype.playFastEffect = function (effectName, loop, bundleName) {
    // 设置默认参数
    if (loop === undefined) {
      loop = false;
    }
    if (bundleName === undefined) {
      bundleName = "audio";
    }

    // 参数验证
    if (!effectName) {
      console.warn("AudioManager: 音效文件名不能为空");
      return Promise.resolve(-1);
    }

    return cc_awaiter(this, undefined, undefined, function () {
      let audioId;
      let audioData;
      const self = this;

      return cc_generator(this, function () {
        audioId = -1;

        // 检查音效开关状态
        if (this._setting && this._setting.effectOn) {
          // 获取或创建音效数据
          audioData = this.audioData[effectName];
          if (audioData) {
            // 停止之前播放的同名音效
            this.stopEffect(audioData.audioid);
          } else {
            audioData = {};
          }

          return [2, new Promise(function (resolve) {
            $9ResLoader.default.loadRes(bundleName, effectName, cc.AudioClip).then(function (audioClip) {
              if (audioClip) {
                audioId = cc.audioEngine.playEffect(audioClip, loop);
                audioData.audioid = audioId;
                self.audioData[effectName] = audioData;
                console.log(`AudioManager: 快速播放音效 ${effectName}, ID: ${audioId}`);
              } else {
                console.error(`AudioManager: 快速播放音效失败 - ${bundleName}/${effectName}`);
                $9LogManager.LogMgr.debug("AudioManager playEffect failed", bundleName, effectName);
              }
              resolve(audioId);
            }).catch(function (error) {
              console.error("AudioManager: 加载音效资源时出错", error);
              resolve(-1);
            });
          })];
        } else {
          return [2, audioId];
        }
      });
    });
  };
  /**
   * 停止指定音效
   *
   * @param {number} audioId - 音效ID
   */
  AudioManager.prototype.stopEffect = function (audioId) {
    if (audioId !== -1 && audioId !== undefined) {
      cc.audioEngine.stopEffect(audioId);
      console.log(`AudioManager: 停止音效 ID: ${audioId}`);
    }
  };

  /**
   * 停止所有音效
   */
  AudioManager.prototype.stopAllEffects = function () {
    cc.audioEngine.stopAllEffects();
    this.audioData = {}; // 清空音效数据缓存
    console.log("AudioManager: 停止所有音效");
  };

  /**
   * 切换音效开关状态
   *
   * @param {boolean} enabled - 是否开启音效
   */
  AudioManager.prototype.switchEffect = function (enabled) {
    if (this._setting) {
      this._setting.effectOn = enabled;

      if (!enabled) {
        this.stopAllEffects();
      }

      this._save("setting", this._setting);
      console.log(`AudioManager: 音效开关 ${enabled ? '开启' : '关闭'}`);
    }
  };

  /**
   * 设置音效音量
   *
   * @param {number} volume - 音量值（0-1）
   */
  AudioManager.prototype.setEffectVolume = function (volume) {
    // 参数验证
    if (volume < 0 || volume > 1) {
      console.warn("AudioManager: 音量值应在0-1之间", volume);
      volume = Math.max(0, Math.min(1, volume));
    }

    if (this._setting) {
      this._setting.effectVolume = volume;
      cc.audioEngine.setEffectsVolume(volume);
      this._setting.effectOn = volume > 0;

      this._save("setting", this._setting);
      console.log(`AudioManager: 设置音效音量 ${volume}`);
    }
  };

  // ==================== 其他设置方法 ====================

  /**
   * 切换震动开关状态
   *
   * @param {boolean} enabled - 是否开启震动
   */
  AudioManager.prototype.switchShake = function (enabled) {
    if (this._setting) {
      this._setting.shakeOn = enabled;
      this._save("setting", this._setting);
      console.log(`AudioManager: 震动开关 ${enabled ? '开启' : '关闭'}`);
    }
  };

  /**
   * 切换辅助线显示状态
   *
   * @param {boolean} enabled - 是否显示辅助线
   */
  AudioManager.prototype.switchFuZhuXian = function (enabled) {
    if (this._setting) {
      this._setting.fuZhuXian = enabled;
      this._save("setting", this._setting);

      // 触发辅助线变化事件
      $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.Setting_ChangeFuZhuXian);

      console.log(`AudioManager: 辅助线显示 ${enabled ? '开启' : '关闭'}`);
    }
  };

  /**
   * 获取当前音频设置信息
   *
   * @returns {Object} 音频设置对象
   */
  AudioManager.prototype.getSettingInfo = function () {
    return this._setting;
  };

  /**
   * 获取音频管理器的统计信息
   *
   * @returns {Object} 包含各种音频统计的对象
   */
  AudioManager.prototype.getAudioStats = function () {
    const stats = {
      currentMusic: this._curMusic,
      musicOn: this.musicOn,
      effectOn: this.effectOn,
      shakeOn: this.shakeOn,
      musicVolume: this._setting ? this._setting.musicVolume : 0.5,
      effectVolume: this._setting ? this._setting.effectVolume : 1.0,
      cachedEffectsCount: Object.keys(this.audioData).length
    };

    return stats;
  };
  /**
   * 重置所有音频设置为默认值
   *
   * 警告：此方法会重置所有音频设置，请谨慎使用
   */
  AudioManager.prototype.resetAllSettings = function () {
    console.warn("AudioManager: 重置所有音频设置");

    try {
      // 停止所有音频
      this.stopMusic();
      this.stopAllEffects();

      // 重置为默认设置
      this._setting = {
        fuZhuXian: true,
        musicVolume: 0.5,
        musicOn: true,
        effectVolume: 1.0,
        effectOn: true,
        shakeOn: true
      };

      // 应用设置并保存
      this._applyAudioSettings();
      this._save("setting", this._setting);

      // 触发辅助线变化事件
      $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.Setting_ChangeFuZhuXian);

      console.log("AudioManager: 所有音频设置重置完成");
    } catch (error) {
      console.error("AudioManager: 重置音频设置时出错", error);
    }
  };

  return AudioManager;

}($9DataManager.default);

// 导出音频管理器单例实例
exports.AudioMgr = AudioManagerClass.getInstance();