/**
 * 游戏数据管理器
 *
 * 功能说明：
 * 1. 管理游戏关卡数据（普通关卡、无尽关卡、直播关卡）
 * 2. 处理游戏背包数据和银币系统
 * 3. 管理玩家进度、奖励记录和排行榜数据
 * 4. 提供关卡状态查询和更新功能
 * 5. 处理游戏存档的加载和保存
 *
 * 核心系统：
 * - 关卡系统：管理不同模式的关卡进度和状态
 * - 背包系统：管理游戏内物品和银币
 * - 存档系统：处理游戏数据的持久化存储
 * - 排行榜系统：上传和管理玩家成绩
 * - 奖励系统：管理关卡奖励和宝箱记录
 *
 * 设计模式：
 * - 单例模式：全局唯一的游戏数据管理器
 * - 观察者模式：数据变化时通知相关系统
 * - 策略模式：不同关卡模式的差异化处理
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_assign = __assign;
const cc_awaiter = __awaiter;
const cc_generator = __generator;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GameDataMgr = undefined;

// 依赖模块导入
const $9DataManager = require("DataManager");
const $9EventManager = require("EventManager");
const $9LogManager = require("LogManager");
const $9SDKManager = require("SDKManager");
const $9TipsManager = require("TipsManager");
const $9Excel = require("Excel");
const $9Const_Game = require("Const_Game");
const $9Logic_Game = require("Logic_Game");
const $9Const_Hall = require("Const_Hall");
const $9EvenType = require("EvenType");
const $9UserData = require("UserData");

/**
 * 游戏数据存储键名枚举
 * 定义所有游戏数据在本地存储中的键名
 */
const GameDataStorageKeys = {
  /** 普通关卡数据存储键 */
  level: "level",

  /** 无尽关卡数据存储键 */
  endlesslevel: "endlesslevel",

  /** 直播无尽关卡数据存储键 */
  liveendlesslevel: "liveendlesslevel",

  /** 无用映射数据存储键 */
  wuYongMap: "wuYongMap",

  /** 游戏背包数据存储键 */
  gameBag: "gameBag",

  /** 无尽模式背包数据存储键 */
  gameBagwujin: "gameBagwujin",

  /** 游戏难度数据存储键 */
  gamenandu: "gamenandu"
};
/**
 * 游戏数据管理器类
 *
 * 继承自DataManager基类，提供完整的游戏数据管理功能
 */
const GameDataManagerClass = function (BaseDataManager) {

  /**
   * 构造函数
   * 初始化游戏数据管理器的所有属性
   */
  function GameDataManager() {
    const instance = BaseDataManager !== null && BaseDataManager.apply(this, arguments) || this;

    // ==================== 基础配置 ====================
    /** 数据加密密钥 */
    instance._secret_key = "game";

    // ==================== 关卡数据 ====================
    /** 普通关卡数据 */
    instance._level = null;

    /** 无尽关卡数据 */
    instance._endlesslevel = null;

    /** 直播无尽关卡数据 */
    instance._liveendlesslevel = null;

    // ==================== 背包数据 ====================
    /** 普通模式游戏背包数据 */
    instance._gameBag = new $9Const_Game.GameBagData.BagSaveData();

    /** 无尽模式游戏背包数据 */
    instance._gameBag_wujin = new $9Const_Game.GameBagData.BagSaveData();

    // ==================== 其他数据 ====================
    /** 无用映射数据（用于特殊功能） */
    instance._wuYongMap = {};

    return instance;
  }

  // 设置继承关系
  cc_extends(GameDataManager, BaseDataManager);
  /**
   * 初始化游戏数据管理器
   *
   * @returns {Promise<void>} 初始化完成的Promise
   *
   * 执行流程：
   * 1. 异步加载所有游戏数据
   * 2. 合并加载的数据到内存中
   * 3. 初始化默认数据结构
   * 4. 完成初始化回调
   */
  GameDataManager.prototype.init = function () {
    const self = this;

    return new Promise(function (resolve) {
      return cc_awaiter(self, undefined, undefined, function () {
        let levelData;
        let endlessLevelData;
        let gameBagData;
        let gameBagWujinData;
        let wuYongMapData;

        return cc_generator(this, function (step) {
          switch (step.label) {
            case 0:
              // 加载普通关卡数据
              return [4, this._load(GameDataStorageKeys.level)];

            case 1:
              levelData = step.sent();
              this._level = cc_assign(cc_assign({}, this._level), levelData);

              // 加载无尽关卡数据
              return [4, this._load(GameDataStorageKeys.endlesslevel)];

            case 2:
              endlessLevelData = step.sent();
              this._endlesslevel = cc_assign(cc_assign({}, this._endlesslevel), endlessLevelData);

              // 加载游戏背包数据
              return [4, this._load(GameDataStorageKeys.gameBag)];

            case 3:
              gameBagData = step.sent();
              this._gameBag = cc_assign(cc_assign({}, this._gameBag), gameBagData);

              // 加载无尽模式背包数据
              return [4, this._load(GameDataStorageKeys.gameBagwujin)];

            case 4:
              gameBagWujinData = step.sent();
              this._gameBag_wujin = cc_assign(cc_assign({}, this._gameBag_wujin), gameBagWujinData);

              // 加载无用映射数据
              return [4, this._load(GameDataStorageKeys.wuYongMap)];

            case 5:
              wuYongMapData = step.sent();
              if (wuYongMapData) {
                this._wuYongMap = wuYongMapData;
              }

              // 完成初始化
              resolve();
              this.initData();

              console.log("GameDataManager: 初始化完成");
              return [2];
          }
        });
      });
    });
  };
  /**
   * 初始化默认数据结构
   *
   * 功能说明：
   * 1. 检查并初始化普通关卡数据
   * 2. 检查并初始化无尽关卡数据
   * 3. 为缺失的数据创建默认值
   * 4. 保存初始化后的数据
   */
  GameDataManager.prototype.initData = function () {
    // 初始化普通关卡数据
    if (!this._level || (this._level && !this._level.levelInfo)) {
      this._level = {
        levelInfo: []
      };
      this.addDefaultLevel(0);
    } else {
      // 确保第一关存在
      if (!this._level.levelInfo[0]) {
        this.addDefaultLevel(0);
      }
    }

    // 初始化无尽关卡数据
    if (!this._endlesslevel || (this._endlesslevel && !this._endlesslevel.levelInfo)) {
      const defaultEndlessLevel = {
        levelInfo: {
          levelId: 1,
          levelCoin: 15,
          ispass: false,
          maxboshu: 1,
          nowboshu: 1,
          words: [],
          wrodselect: 10,
          wordallhave: $9Logic_Game.default.allHaveMaxCount,
          playerlevel: 1,
          fuhuocount: $9Logic_Game.default.fuhuoCount,
          gameaward: [],
          isback: false
        }
      };

      this._endlesslevel = defaultEndlessLevel;
      this._save(GameDataStorageKeys.endlesslevel, this._endlesslevel);

      console.log("GameDataManager: 初始化无尽关卡数据");
    }

    console.log("GameDataManager: 数据初始化完成");
  };
  /**
   * 添加默认关卡数据
   *
   * @param {number} levelIndex - 关卡索引（从0开始）
   *
   * 功能说明：
   * 1. 检查指定索引的关卡是否存在
   * 2. 如果不存在则创建默认关卡数据
   * 3. 保存更新后的关卡数据
   */
  GameDataManager.prototype.addDefaultLevel = function (levelIndex) {
    // 参数验证
    if (levelIndex < 0) {
      console.error("GameDataManager: 关卡索引不能为负数", levelIndex);
      return;
    }

    let levelData = this._level.levelInfo[levelIndex];

    // 如果关卡数据不存在，创建默认数据
    if (!levelData) {
      levelData = {
        levelId: levelIndex + 1,
        levelCoin: 15,
        ispass: false,
        maxboshu: 1,
        nowboshu: 1,
        words: [],
        wrodselect: 10,
        wordallhave: $9Logic_Game.default.allHaveMaxCount,
        playerlevel: 1,
        fuhuocount: $9Logic_Game.default.fuhuoCount,
        gameaward: []
      };

      this._level.levelInfo.push(levelData);
      this._save(GameDataStorageKeys.level, this._level);

      console.log(`GameDataManager: 添加默认关卡 ${levelIndex + 1}`);
    }
  };
  /**
   * 获取当前关卡信息
   *
   * @param {number} levelId - 关卡ID（从1开始，默认为1）
   * @param {number} levelMode - 关卡模式（默认为普通关卡）
   * @returns {Object|null} 关卡信息对象，如果不存在则返回null
   *
   * 支持的关卡模式：
   * - 普通关卡：返回指定ID的普通关卡信息
   * - 无尽关卡：返回无尽关卡信息
   * - 直播无尽关卡：返回直播无尽关卡信息
   */
  GameDataManager.prototype.getCurrentLevelInfo = function (levelId, levelMode) {
    // 设置默认参数
    if (levelId === undefined) {
      levelId = 1;
    }
    if (levelMode === undefined) {
      levelMode = $9Const_Game.levelMode.普通关卡;
    }

    let levelInfo = null;

    // 根据关卡模式获取相应的关卡信息
    switch (levelMode) {
      case $9Const_Game.levelMode.普通关卡:
        if (this._level && this._level.levelInfo) {
          levelInfo = this._level.levelInfo[levelId - 1];
        }
        break;

      case $9Const_Game.levelMode.无尽关卡:
        if (this._endlesslevel) {
          levelInfo = this._endlesslevel.levelInfo;
        }
        break;

      case $9Const_Game.levelMode.直播无尽关卡:
        if (this._liveendlesslevel) {
          levelInfo = this._liveendlesslevel.levelInfo;
        }
        break;

      default:
        console.warn(`GameDataManager: 未知的关卡模式 ${levelMode}`);
        break;
    }

    return levelInfo;
  };
  /**
   * 保存关卡信息
   *
   * @param {number} levelId - 关卡ID（从1开始，默认为1）
   * @param {number} levelMode - 关卡模式（默认为普通关卡）
   * @param {Object} levelInfo - 要保存的关卡信息对象
   *
   * 功能说明：
   * 1. 根据关卡模式将数据保存到相应的存储位置
   * 2. 立即持久化数据到本地存储
   * 3. 提供错误处理和日志记录
   */
  GameDataManager.prototype.saveLevelInfo = function (levelId, levelMode, levelInfo) {
    // 设置默认参数
    if (levelId === undefined) {
      levelId = 1;
    }
    if (levelMode === undefined) {
      levelMode = $9Const_Game.levelMode.普通关卡;
    }

    // 参数验证
    if (!levelInfo) {
      console.error("GameDataManager: 关卡信息不能为空");
      return;
    }

    try {
      // 根据关卡模式保存数据
      switch (levelMode) {
        case $9Const_Game.levelMode.普通关卡:
          if (this._level && this._level.levelInfo) {
            this._level.levelInfo[levelId - 1] = levelInfo;
            this._save(GameDataStorageKeys.level, this._level);
            console.log(`GameDataManager: 保存普通关卡 ${levelId} 信息`);
          }
          break;

        case $9Const_Game.levelMode.无尽关卡:
          if (this._endlesslevel) {
            this._endlesslevel.levelInfo = levelInfo;
            this._save(GameDataStorageKeys.endlesslevel, this._endlesslevel);
            console.log("GameDataManager: 保存无尽关卡信息");
          }
          break;

        case $9Const_Game.levelMode.直播无尽关卡:
          if (this._liveendlesslevel) {
            this._liveendlesslevel.levelInfo = levelInfo;
            this._save(GameDataStorageKeys.liveendlesslevel, this._liveendlesslevel);
            console.log("GameDataManager: 保存直播无尽关卡信息");
          }
          break;

        default:
          console.error(`GameDataManager: 未知的关卡模式 ${levelMode}`);
          break;
      }
    } catch (error) {
      console.error("GameDataManager: 保存关卡信息时出错", error);
    }
  };
  /**
   * 清理关卡数据（重置为默认状态）
   *
   * @param {number} levelId - 关卡ID
   * @param {number} levelMode - 关卡模式（默认为普通关卡）
   *
   * 功能说明：
   * 1. 重置关卡的游戏进度数据
   * 2. 恢复默认的金币、波数、词汇等数据
   * 3. 清空奖励记录和玩家等级
   * 4. 保存重置后的数据
   */
  GameDataManager.prototype.cleanLevelData = function (levelId, levelMode) {
    // 设置默认参数
    if (levelMode === undefined) {
      levelMode = $9Const_Game.levelMode.普通关卡;
    }

    // 获取当前关卡信息
    const levelInfo = this.getCurrentLevelInfo(levelId, levelMode);

    if (!levelInfo) {
      console.warn(`GameDataManager: 关卡 ${levelId} 不存在，无法清理`);
      return;
    }

    // 根据当前游戏模式进行清理
    switch ($9Logic_Game.default.levelMode) {
      case $9Const_Game.levelMode.普通关卡:
        this._resetLevelToDefault(levelInfo);
        break;

      case $9Const_Game.levelMode.无尽关卡:
        this._resetEndlessLevelToDefault(levelInfo);
        break;

      default:
        console.warn(`GameDataManager: 未知的游戏模式 ${$9Logic_Game.default.levelMode}`);
        break;
    }

    // 保存清理后的数据
    this.saveLevelInfo(levelId, levelMode, levelInfo);
    console.log(`GameDataManager: 清理关卡 ${levelId} 数据完成`);
  };

  /**
   * 清理关卡数据（兼容性方法）
   *
   * @param {number} levelId - 关卡ID
   * @param {number} levelMode - 关卡模式（默认为普通关卡）
   * @deprecated 请使用 cleanLevelData 方法
   */
  GameDataManager.prototype.cleanDefLevel = function (levelId, levelMode) {
    console.warn("GameDataManager: cleanDefLevel 方法已废弃，请使用 cleanLevelData");
    return this.cleanLevelData(levelId, levelMode);
  };

  /**
   * 重置普通关卡到默认状态
   * @private
   */
  GameDataManager.prototype._resetLevelToDefault = function (levelInfo) {
    levelInfo.levelCoin = 15;
    levelInfo.nowboshu = 1;
    levelInfo.words = [];
    levelInfo.fuhuocount = $9Logic_Game.default.fuhuoCount;
    levelInfo.wrodselect = $9Logic_Game.default.resetMaxCount;
    levelInfo.wordallhave = $9Logic_Game.default.allHaveMaxCount;
    levelInfo.playerlevel = 1;
    levelInfo.gameaward = [];
  };

  /**
   * 重置无尽关卡到默认状态
   * @private
   */
  GameDataManager.prototype._resetEndlessLevelToDefault = function (levelInfo) {
    levelInfo.levelCoin = 15;
    levelInfo.nowboshu = 1;
    levelInfo.words = [];
    levelInfo.wrodselect = $9Logic_Game.default.resetMaxCount;
    levelInfo.wordallhave = $9Logic_Game.default.allHaveMaxCount;
    levelInfo.playerlevel = 1;
    levelInfo.gameaward = [];
  };

  /**
   * 获取正在进行的游戏数据
   *
   * @param {number} levelMode - 关卡模式（默认为普通关卡）
   * @returns {Object|null} 正在进行的关卡数据
   */
  GameDataManager.prototype.getGameOnData = function (levelMode) {
    if (levelMode === undefined) {
      levelMode = $9Const_Game.levelMode.普通关卡;
    }

    let ongoingLevelData = null;

    switch (levelMode) {
      case $9Const_Game.levelMode.普通关卡:
        const levelInfoArray = this._level.levelInfo;
        for (let i = 0; i < levelInfoArray.length; i++) {
          const levelInfo = levelInfoArray[i];
          if (levelInfo.nowboshu > 1) {
            ongoingLevelData = levelInfo;
            break;
          }
          this.cleanLevelData(levelInfo.levelId);
        }
        break;

      case $9Const_Game.levelMode.无尽关卡:
        ongoingLevelData = this._endlesslevel.levelInfo;
        break;
    }

    return ongoingLevelData;
  };

  /**
   * 获取最大关卡数（第一个未通过的关卡索引）
   *
   * @returns {number} 最大关卡索引
   */
  GameDataManager.prototype.getMaxLevel = function () {
    return this._level.levelInfo.findIndex(function (levelInfo) {
      return !levelInfo.ispass;
    });
  };

  /**
   * 获取无尽关卡当前波数
   *
   * @returns {number} 当前波数
   */
  GameDataManager.prototype.getEndlessLevel = function () {
    return this._endlesslevel.levelInfo.nowboshu;
  };

  /**
   * 获取无尽关卡最大波数
   *
   * @returns {number} 最大波数
   */
  GameDataManager.prototype.getEndlessMaxLevel = function () {
    return this._endlesslevel.levelInfo.maxboshu;
  };

  /**
   * 检查关卡是否已通过
   *
   * @param {number} levelId - 关卡ID
   * @returns {boolean} 是否已通过
   */
  GameDataManager.prototype.isLevelPass = function (levelId) {
    const levelInfo = this._level.levelInfo[levelId - 1];
    return !!levelInfo && levelInfo.ispass;
  };

  /**
   * 检查关卡是否已解锁
   *
   * @param {number} levelId - 关卡ID
   * @returns {boolean} 是否已解锁
   */
  GameDataManager.prototype.isLevelUnlocked = function (levelId) {
    return !!this._level.levelInfo[levelId - 1];
  };

  /**
   * 设置关卡为已通过状态
   *
   * @param {number} levelId - 关卡ID
   */
  GameDataManager.prototype.setLevelPass = function (levelId) {
    const levelInfoArray = this._level.levelInfo;
    const levelInfo = levelInfoArray[levelId - 1];

    // 清理关卡数据
    this.cleanLevelData(levelId);

    if (levelInfo && !levelInfo.ispass) {
      levelInfo.ispass = true;
      levelInfoArray[levelId - 1] = levelInfo;
      this._level.levelInfo = levelInfoArray;

      // 添加下一关
      this.addDefaultLevel(levelId);

      // 保存数据
      this._save(GameDataStorageKeys.level, this._level);

      // 提交排行榜
      this.submitRank($9Const_Hall.RankType.Rank_level, levelId);
    }
  };

  /**
   * 设置关卡最大波数
   *
   * @param {number} levelId - 关卡ID
   */
  GameDataManager.prototype.setLevelMaxBoShu = function (levelId) {
    const levelInfo = exports.GameDataMgr._level.levelInfo[levelId - 1];
    if (levelInfo) {
      const levelConfig = $9Excel.Excel.haizhanlevel(levelId);
      if (levelConfig) {
        levelInfo.maxboshu = levelConfig.levelboshuid.length + 1;
      }
    }
  };

  /**
   * 提交排行榜数据
   *
   * @param {number} rankType - 排行榜类型
   * @param {number} value - 提交的数值
   */
  GameDataManager.prototype.submitRank = function (rankType, value) {
    const rankData = {
      rank_id: rankType,
      value: value,
      nickname: $9UserData.User.platfrom.nickName,
      avatar: $9UserData.User.platfrom.avatarUrl,
      success: function (result) {
        $9LogManager.LogMgr.info(`上传通关记录成功 ${rankType} - value: ${value}`, result);
      },
      fail: function (error) {
        $9LogManager.LogMgr.info("上传通关记录失败", error);
      }
    };

    $9SDKManager.SDKMgr.submitRankData(rankData);
  };

  /**
   * 检查关卡是否已解锁（兼容性方法）
   *
   * @param {number} levelId - 关卡ID
   * @returns {boolean} 是否已解锁
   * @deprecated 请使用 isLevelUnlocked 方法
   */
  GameDataManager.prototype.isLevelLock = function (levelId) {
    console.warn("GameDataManager: isLevelLock 方法已废弃，请使用 isLevelUnlocked");
    return this.isLevelUnlocked(levelId);
  };

  /**
   * 获取无尽关卡当前波数（兼容性方法）
   *
   * @returns {number} 当前波数
   * @deprecated 请使用 getEndlessLevel 方法
   */
  GameDataManager.prototype.getEndLessLevel = function () {
    console.warn("GameDataManager: getEndLessLevel 方法已废弃，请使用 getEndlessLevel");
    return this.getEndlessLevel();
  };

  /**
   * 获取无尽关卡最大波数（兼容性方法）
   *
   * @returns {number} 最大波数
   * @deprecated 请使用 getEndlessMaxLevel 方法
   */
  GameDataManager.prototype.getEndLessMaxLevel = function () {
    console.warn("GameDataManager: getEndLessMaxLevel 方法已废弃，请使用 getEndlessMaxLevel");
    return this.getEndlessMaxLevel();
  };

  /**
   * 设置无尽关卡返回状态（兼容性方法）
   *
   * @param {boolean} isBack - 是否返回状态
   */
  GameDataManager.prototype.setEndLessLevelBack = function (isBack) {
    console.warn("GameDataManager: setEndLessLevelBack 方法已废弃");
    const levelInfo = this.getCurrentLevelInfo(1, $9Const_Game.levelMode.无尽关卡);
    if (levelInfo) {
      levelInfo.isback = isBack;
      this.saveLevelInfo(1, $9Const_Game.levelMode.无尽关卡, levelInfo);
    }
  };

  /**
   * 获取关卡玩家当前波数
   *
   * @param {number} levelId - 关卡ID
   * @param {number} levelMode - 关卡模式（默认为普通关卡）
   * @returns {number} 当前波数
   */
  GameDataManager.prototype.getLevelPlayerNowBoShu = function (levelId, levelMode) {
    if (levelMode === undefined) {
      levelMode = $9Const_Game.levelMode.普通关卡;
    }

    const levelInfo = this.getCurrentLevelInfo(levelId, levelMode);
    if (levelInfo) {
      return levelInfo.maxboshu || 1;
    } else {
      return 1;
    }
  };

  /**
   * 获取关卡宝箱记录
   *
   * @param {number} levelId - 关卡ID
   * @param {number} boxIndex - 宝箱索引
   * @param {number} levelMode - 关卡模式（默认为普通关卡）
   * @returns {boolean} 是否已获得该宝箱
   */
  GameDataManager.prototype.getLevelBoxRecords = function (levelId, boxIndex, levelMode) {
    if (levelMode === undefined) {
      levelMode = $9Const_Game.levelMode.普通关卡;
    }

    const levelInfo = this.getCurrentLevelInfo(levelId, levelMode);
    return !!(levelInfo && levelInfo.awardrecord && levelInfo.awardrecord[boxIndex]);
  };

  /**
   * 设置关卡宝箱记录
   *
   * @param {number} levelId - 关卡ID
   * @param {number} boxIndex - 宝箱索引
   * @param {number} levelMode - 关卡模式（默认为普通关卡）
   */
  GameDataManager.prototype.setLevelBoxRecords = function (levelId, boxIndex, levelMode) {
    if (levelMode === undefined) {
      levelMode = $9Const_Game.levelMode.普通关卡;
    }

    const levelInfo = this.getCurrentLevelInfo(levelId, levelMode);
    if (levelInfo) {
      if (!levelInfo.awardrecord) {
        levelInfo.awardrecord = {};
      }
      levelInfo.awardrecord[boxIndex] = "1";
      this.saveLevelInfo(levelId, levelMode, levelInfo);
    }
  };

  // ==================== 背包系统方法 ====================

  /**
   * 游戏背包数据访问器
   * 根据当前游戏模式返回相应的背包数据
   */
  Object.defineProperty(GameDataManager.prototype, "gameBag", {
    get: function () {
      if ($9Logic_Game.default.levelMode === $9Const_Game.levelMode.无尽关卡) {
        return this._gameBag_wujin;
      } else {
        return this._gameBag;
      }
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 清理游戏背包数据
   *
   * @param {number} levelMode - 关卡模式
   *
   * 功能说明：
   * 1. 根据关卡模式清理相应的背包数据
   * 2. 重置背包为初始状态
   * 3. 保存清理后的数据
   */
  GameDataManager.prototype.clearGameBagData = function (levelMode) {
    // 参数验证
    if (levelMode <= 0) {
      console.error("GameDataManager: 清理背包时传入了无效的关卡模式", levelMode);
      return;
    }

    try {
      // 根据关卡模式清理相应的背包
      switch (levelMode) {
        case $9Const_Game.levelMode.无尽关卡:
          this._gameBag_wujin = new $9Const_Game.GameBagData.BagSaveData();
          console.log("GameDataManager: 清理无尽关卡背包数据");
          break;

        default:
          this._gameBag = new $9Const_Game.GameBagData.BagSaveData();
          console.log("GameDataManager: 清理普通关卡背包数据");
          break;
      }

      // 保存清理后的数据
      this.saveGameBagData();
    } catch (error) {
      console.error("GameDataManager: 清理背包数据时出错", error);
    }
  };

  /**
   * 保存游戏背包数据
   *
   * 功能说明：
   * 1. 根据当前游戏模式保存相应的背包数据
   * 2. 立即持久化到本地存储
   */
  GameDataManager.prototype.saveGameBagData = function () {
    try {
      if ($9Logic_Game.default.levelMode === $9Const_Game.levelMode.无尽关卡) {
        this._save(GameDataStorageKeys.gameBagwujin, this._gameBag_wujin);
        console.log("GameDataManager: 保存无尽关卡背包数据");
      } else {
        this._save(GameDataStorageKeys.gameBag, this._gameBag);
        console.log("GameDataManager: 保存普通关卡背包数据");
      }
    } catch (error) {
      console.error("GameDataManager: 保存背包数据时出错", error);
    }
  };

  /**
   * 清理游戏背包数据（兼容性方法）
   *
   * @param {number} levelMode - 关卡模式
   * @deprecated 请使用 clearGameBagData 方法
   */
  GameDataManager.prototype.ClearGameBagData = function (levelMode) {
    console.warn("GameDataManager: ClearGameBagData 方法已废弃，请使用 clearGameBagData");
    return this.clearGameBagData(levelMode);
  };

  /**
   * 保存游戏背包数据（兼容性方法）
   *
   * @deprecated 请使用 saveGameBagData 方法
   */
  GameDataManager.prototype.SaveGameBagData = function () {
    console.warn("GameDataManager: SaveGameBagData 方法已废弃，请使用 saveGameBagData");
    return this.saveGameBagData();
  };

  // ==================== 银币系统方法 ====================

  /**
   * 银币数量访问器
   * 获取当前背包中的银币数量
   */
  Object.defineProperty(GameDataManager.prototype, "silverCoin", {
    get: function () {
      return this.gameBag.silverCoin || 0;
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 银币数量访问器（兼容性属性）
   * 获取当前背包中的银币数量
   * @deprecated 请使用 silverCoin 属性
   */
  Object.defineProperty(GameDataManager.prototype, "sliderCoin", {
    get: function () {
      console.warn("GameDataManager: sliderCoin 属性已废弃，请使用 silverCoin");
      return this.silverCoin;
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 添加银币
   *
   * @param {number} amount - 要添加的银币数量
   *
   * 功能说明：
   * 1. 验证输入的银币数量
   * 2. 增加背包中的银币
   * 3. 触发银币变化事件
   * 4. 保存更新后的背包数据
   */
  GameDataManager.prototype.addSilverCoin = function (amount) {
    // 参数验证
    if (Number.isNaN(amount) || amount <= 0) {
      console.warn("GameDataManager: 添加银币数量无效", amount);
      return;
    }

    try {
      // 增加银币
      this.gameBag.silverCoin += amount;

      // 触发银币变化事件
      $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.Game_YinBiValue);

      // 保存数据
      this.saveGameBagData();

      console.log(`GameDataManager: 添加银币 ${amount}，当前银币: ${this.gameBag.silverCoin}`);
    } catch (error) {
      console.error("GameDataManager: 添加银币时出错", error);
    }
  };

  /**
   * 扣除银币
   *
   * @param {number} amount - 要扣除的银币数量
   * @returns {boolean} 扣除是否成功
   *
   * 功能说明：
   * 1. 验证输入的银币数量
   * 2. 检查银币是否足够
   * 3. 扣除银币并更新数据
   * 4. 触发银币变化事件
   */
  GameDataManager.prototype.subtractSilverCoin = function (amount) {
    // 参数验证
    if (Number.isNaN(amount) || amount <= 0) {
      console.warn("GameDataManager: 扣除银币数量无效", amount);
      return false;
    }

    try {
      // 检查银币是否足够
      if (this.gameBag.silverCoin < amount) {
        $9TipsManager.TipMgr.showToast("银币不足");
        return false;
      }

      // 扣除银币
      this.gameBag.silverCoin -= amount;

      // 触发银币变化事件
      $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.Game_YinBiValue);

      // 保存数据
      this.saveGameBagData();

      console.log(`GameDataManager: 扣除银币 ${amount}，当前银币: ${this.gameBag.silverCoin}`);
      return true;
    } catch (error) {
      console.error("GameDataManager: 扣除银币时出错", error);
      return false;
    }
  };

  /**
   * 添加银币（兼容性方法）
   *
   * @param {number} amount - 要添加的银币数量
   * @deprecated 请使用 addSilverCoin 方法
   */
  GameDataManager.prototype.AddSliderCoin = function (amount) {
    console.warn("GameDataManager: AddSliderCoin 方法已废弃，请使用 addSilverCoin");
    return this.addSilverCoin(amount);
  };

  /**
   * 扣除银币（兼容性方法）
   *
   * @param {number} amount - 要扣除的银币数量
   * @returns {boolean} 扣除是否成功
   * @deprecated 请使用 subtractSilverCoin 方法
   */
  GameDataManager.prototype.SubSliderCoin = function (amount) {
    console.warn("GameDataManager: SubSliderCoin 方法已废弃，请使用 subtractSilverCoin");
    return this.subtractSilverCoin(amount);
  };

  // ==================== 无用映射系统方法 ====================

  /**
   * 无用映射数据访问器
   * 用于存储特殊功能的映射数据
   */
  Object.defineProperty(GameDataManager.prototype, "wuYongMap", {
    get: function () {
      if (!this._wuYongMap) {
        this._wuYongMap = {};
      }
      return this._wuYongMap;
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 保存无用映射数据
   *
   * 功能说明：
   * 1. 将无用映射数据持久化到本地存储
   * 2. 提供错误处理和日志记录
   */
  GameDataManager.prototype.saveWuYongMap = function () {
    try {
      this._save(GameDataStorageKeys.wuYongMap, this._wuYongMap);
      console.log("GameDataManager: 保存无用映射数据");
    } catch (error) {
      console.error("GameDataManager: 保存无用映射数据时出错", error);
    }
  };

  /**
   * 获取游戏数据管理器的统计信息
   *
   * @returns {Object} 包含各种数据统计的对象
   */
  GameDataManager.prototype.getDataStats = function () {
    const stats = {
      levelCount: this._level ? this._level.levelInfo.length : 0,
      hasEndlessLevel: !!this._endlesslevel,
      hasLiveEndlessLevel: !!this._liveendlesslevel,
      silverCoin: this.silverCoin,
      wuYongMapKeys: Object.keys(this.wuYongMap).length
    };

    return stats;
  };

  /**
   * 重置所有游戏数据
   *
   * 警告：此方法会清除所有游戏进度，请谨慎使用
   */
  GameDataManager.prototype.resetAllData = function () {
    console.warn("GameDataManager: 重置所有游戏数据");

    try {
      // 重置关卡数据
      this._level = { levelInfo: [] };
      this._endlesslevel = null;
      this._liveendlesslevel = null;

      // 重置背包数据
      this._gameBag = new $9Const_Game.GameBagData.BagSaveData();
      this._gameBag_wujin = new $9Const_Game.GameBagData.BagSaveData();

      // 重置映射数据
      this._wuYongMap = {};

      // 保存重置后的数据
      this._save(GameDataStorageKeys.level, this._level);
      this._save(GameDataStorageKeys.gameBag, this._gameBag);
      this._save(GameDataStorageKeys.gameBagwujin, this._gameBag_wujin);
      this._save(GameDataStorageKeys.wuYongMap, this._wuYongMap);

      // 重新初始化数据
      this.initData();

      console.log("GameDataManager: 所有游戏数据重置完成");
    } catch (error) {
      console.error("GameDataManager: 重置游戏数据时出错", error);
    }
  };

  return GameDataManager;

}($9DataManager.default);

// 导出游戏数据管理器单例实例
exports.GameDataMgr = GameDataManagerClass.getInstance();