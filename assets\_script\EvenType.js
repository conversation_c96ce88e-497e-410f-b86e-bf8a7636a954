/**
 * 游戏事件类型定义
 *
 * 功能说明：
 * 1. 定义游戏中所有事件的标识符常量
 * 2. 按功能模块分类组织事件类型
 * 3. 提供类型安全的事件名称引用
 *
 * 设计原则：
 * - 使用枚举模式确保事件名称的唯一性
 * - 按模块分组便于维护和查找
 * - 使用描述性命名提高代码可读性
 *
 * <AUTHOR>
 * @version 2.0.0
 */

Object.defineProperty(exports, "__esModule", {
  value: true
});

exports.EVENT_TYPE = undefined;

/**
 * 游戏事件类型枚举
 *
 * 按功能模块分类定义所有游戏事件：
 * - 系统级事件：视图、全局交互等
 * - 游戏核心事件：游戏状态、战斗、相机等
 * - UI界面事件：大厅、商店、背包等
 * - 引导系统事件：新手引导相关
 * - 广告系统事件：广告显示和奖励
 * - 小游戏事件：各种小游戏模式
 */
(function (EventTypes) {

  // ==================== 系统级事件 ====================
  /** 视图尺寸改变事件 */
  EventTypes.View_Resize = "view-resize";

  /** 富文本点击事件 */
  EventTypes.RichText_Click = "RichText_Click";

  /** 全局点击事件 */
  EventTypes.Global_Click = "Global_Click";

  /** 设置-辅助线显示状态改变 */
  EventTypes.Setting_ChangeFuZhuXian = "Setting_ChangeFuZhuXian";

  // ==================== 游戏核心事件 ====================
  /** 游戏银币数值变化 */
  EventTypes.Game_YinBiValue = "Game_YinBiValue";

  /** 游戏增加银币 */
  EventTypes.Game_Add_YinBi = "Game_Add_YinBi";

  /** 章节变化事件 */
  EventTypes.ChapterChange = "ChapterChange";

  /** 玩家开始游戏 */
  EventTypes.Player_Game_Open = "Player_Game_Open";

  /** 游戏状态更新 */
  EventTypes.Game_Update = "Game_Update";

  /** 游戏复活 */
  EventTypes.Game_Revive = "Game_Revive";

  /** 禁用游戏操作 */
  EventTypes.Game_Disable_Operate = "Game_Disable_Operate";

  /** 游戏暂停 */
  EventTypes.Game_Pause = "Game_Pause";

  /** 游戏恢复 */
  EventTypes.Game_Resume = "Game_Resume";

  /** 游戏结果事件 */
  EventTypes.Game_Result = "Game_Result";

  /** 游戏增加经验 */
  EventTypes.Game_Add_Exp = "Game_Add_Exp";

  /** 游戏时间结束 */
  EventTypes.Game_Time_Over = "Game_Time_Over";

  /** 游戏获得宝箱 */
  EventTypes.Game_Get_Box = "Game_Get_Box";

  /** 游戏任务完成 */
  EventTypes.Game_TaskOver = "Game_TaskOver";

  // ==================== 相机系统事件 ====================
  /** 相机跟随目标 */
  EventTypes.Game_Camera_Follow = "Game_Camera_Follow";

  /** 相机缩放 */
  EventTypes.Game_Camera_Scale = "Game_Camera_Scale";

  /** 相机边界设置 */
  EventTypes.Game_Camera_Box = "Game_Camera_Box";

  /** 相机重置 */
  EventTypes.Game_Camera_Reset = "Game_Camera_Reset";

  /** 相机锁定 */
  EventTypes.Game_Camera_Lock = "Game_Camera_Lock";

  // ==================== 战斗系统事件 ====================
  /** 武器死亡事件 */
  EventTypes.Game_Weapon_Die = "Game_Weapon_Die";

  /** 武器血量变化 */
  EventTypes.Game_Weapon_Hp = "Game_Weapon_Hp";

  /** 创建怪物 */
  EventTypes.Game_Create_Moster = "Game_Create_Moster";

  /** 显示Boss */
  EventTypes.Game_Show_Boos = "Game_Show_Boos";

  /** 发送攻击子弹 */
  EventTypes.send_attack_bullet = "send_attack_bullet";

  /** 创建子弹 */
  EventTypes.create_bullet = "create_bullet";

  // ==================== 交互系统事件 ====================
  /** 摇杆触摸开始 */
  EventTypes.Game_Rock_TouchStart = "Game_Rock_TouchStart";

  /** 摇杆触摸结束 */
  EventTypes.Game_Rock_TouchEnd = "Game_Rock_TouchEnd";

  /** 显示加钱界面 */
  EventTypes.Game_Show_AddMoneyView = "Game_Show_AddMoneyView";

  /** 显示文字提示 */
  EventTypes.Game_Show_Word = "Game_Show_Word";

  // ==================== 加载和界面事件 ====================
  /** 游戏加载视图 */
  EventTypes.Game_Load_View = "Game_Load_View";

  /** 打开游戏 */
  EventTypes.Game_OpenGame = "Game_OpenGame";

  /** 演示隐藏 */
  EventTypes.Game_YanShi_Hide = "Game_YanShi_Hide";

  /** 设置潜艇水面显示 */
  EventTypes.Game_SetQianTingShuiShow = "Game_SetQianTingShuiShow";

  // ==================== 数据统计事件 ====================
  /** 游戏伤害统计 */
  EventTypes.GAME_SHANGHAI_TONGJI = "GAME_SHANGHAI_TONGJI";

  // ==================== 大厅界面事件 ====================
  /** 重置商店宝箱 */
  EventTypes.HallResetShopBox = "HallResetShopBox";

  /** 商店滚动 */
  EventTypes.HallScrollShop = "HallScrollShop";

  /** 重置道具数量 */
  EventTypes.HallResetPropCount = "HallResetPropCount";

  /** 重置富文本 */
  EventTypes.Hall_ResetRich = "Hall_ResetRich";

  /** 签到更新 */
  EventTypes.HOME_QIANDAO_UPDATE = "HOME_QIANDAO_UPDATE";

  /** 主红点变化 */
  EventTypes.MainRedPointChange = "MainRedPointChange";

  /** 重置广告券 */
  EventTypes.ResetAdvoucher = "ResetAdvoucher";

  /** 大厅背包显示拖拽 */
  EventTypes.HallBagShowDrag = "HallBagShowDrag";

  /** 大厅背包移除ID */
  EventTypes.HallBagRemoveId = "HallBagRemoveId";

  /** 大厅重置背包 */
  EventTypes.HallResetBag = "HallResetBag";

  /** 大厅模块切换 */
  EventTypes.Hall_ChangeModule = "Hall_ChangeModule";

  /** 大厅重置主舰星级 */
  EventTypes.HallResetMainShipStar = "HallResetMainShipStar";

  /** 大厅章节索引变化 */
  EventTypes.HallChangeChapterIndex = "HallChangeChapterIndex";

  /** 获得新解锁ID */
  EventTypes.GetNewUnlockId = "GetNewUnlockId";

  /** 大厅按钮飞行动画 */
  EventTypes.HallBtnFly = "HallBtnFly";

  // ==================== 引导系统事件 ====================
  /** 引导触摸中 */
  EventTypes.Guide_Touching = "Guide_Touching";

  /** 引导触摸修正 */
  EventTypes.Guide_Touch_Revise = "Guide_Touch_Revise";

  /** 引导完成 */
  EventTypes.Guide_Finish = "Guide_Finish";

  /** 隐藏引导显示 */
  EventTypes.HideGuideShow = "HideGuideShow";

  // ==================== 广告系统事件 ====================
  /** 广告显示 */
  EventTypes.AD_Show = "AD_Show";

  /** 广告开始显示 */
  EventTypes.AD_Show_Start = "AD_Show_Start";

  /** 广告奖励显示 */
  EventTypes.AD_Show_Reward = "AD_Show_Reward";

  // ==================== 小游戏事件 ====================
  /** 小游戏重置时间 */
  EventTypes.LittleGameResetTime = "LittleGameResetTime";

  /** 小游戏开始怪物 */
  EventTypes.mingame_startmoster = "mingame_startmoster";

  /** 小游戏攻击减血 */
  EventTypes.mingame_attack_jianxue = "mingame_attack_jianxue";

  /** 小游戏结束 */
  EventTypes.mingame_gameover = "mingame_gameover";

  /** 小游戏切换攻击 */
  EventTypes.mingame_switch_attack = "mingame_switch_attack";

  /** 小游戏返回主页 */
  EventTypes.mingame_back_home = "mingame_back_home";

  /** 小游戏怪物死亡 */
  EventTypes.mingame_moster_die = "mingame_moster_die";

  /** 小游戏攻击更新 */
  EventTypes.mingame_attack_update = "mingame_attack_update";

  /** 小游戏斗殴小兵 */
  EventTypes.mingame_douou_xiaobin = "mingame_douou_xiaobin";

  /** 小游戏斗殴结束 */
  EventTypes.mingame_douou_gameover = "mingame_douou_gameover";

  /** 小游戏下一关 */
  EventTypes.mingame_next_game = "mingame_next_game";

  /** 小游戏11合成 */
  EventTypes.mingame11_hecheng = "mingame11_hecheng";

  // ==================== 特殊关卡事件 ====================
  /** 关卡2飞行提示 */
  EventTypes.gmLevel2_flytip = "gmLevel2_flytip";

  /** 关卡2打开词条信息 */
  EventTypes.gmLevel2_openWordInfo = "gmLevel2_openWordInfo";

  /** 关卡2升级 */
  EventTypes.gmLevel2_upLevel = "gmLevel2_upLevel";

  /** 关卡2经验变化 */
  EventTypes.gmLevel2_expChange = "gmLevel2_expChange";

  /** 关卡2合成效果 */
  EventTypes.gmLevel2_heChengEffevt = "gmLevel2_heChengEffevt";

  /** 关卡2玩家受击 */
  EventTypes.gmLevel2_onPlayerHit = "gmLevel2_onPlayerHit";

})(exports.EVENT_TYPE || (exports.EVENT_TYPE = {}));