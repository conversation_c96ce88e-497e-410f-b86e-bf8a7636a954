/**
 * 入口UI配置文件
 *
 * 功能说明：
 * 1. 定义游戏入口阶段所有UI界面的标识符
 * 2. 配置每个UI界面的预制体路径和显示属性
 * 3. 向UI管理器注册入口UI配置
 *
 * 配置说明：
 * - prefab: UI预制体在resources目录下的相对路径
 * - block: 是否阻塞用户输入（模态对话框设为true）
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

// 清空导出对象
exports.UIConfig_Entry = exports.UIView_Entry = undefined;

// 导入UI管理器
const UIManager = require("UIManager");

/**
 * 入口UI视图标识符枚举
 *
 * 定义了游戏入口阶段所有可能用到的UI界面名称
 * 使用常量定义避免硬编码字符串，提高代码可维护性
 */
exports.UIView_Entry = {
  /** 主入口界面 - 游戏启动后的第一个界面 */
  UI_Entry: "UI_Entry",

  /** 提示消息界面 - 显示临时提示信息 */
  UI_Toast: "UI_Toast",

  /** 加载界面 - 显示资源加载进度 */
  UI_Loading: "UI_Loading",

  /** 调试界面 - 开发阶段的调试工具 */
  UI_Debug: "UI_Debug",

  /** 对话框界面 - 显示确认、警告等对话框 */
  UI_Dialog: "UI_Dialog",

  /** 子游戏界面 - 小游戏入口界面 */
  UI_Subgame: "UI_Subgame"
};

/**
 * 入口UI配置对象
 *
 * 为每个UI界面配置其预制体路径和显示属性
 *
 * 配置项说明：
 * - prefab: 预制体文件路径（相对于resources目录）
 * - block: 是否阻塞背景交互（true=模态显示，false=非模态显示）
 */
exports.UIConfig_Entry = {
  // 主入口界面配置
  [exports.UIView_Entry.UI_Entry]: {
    prefab: "common/UI_Entry",
    block: true  // 阻塞交互，作为主界面独占显示
  },

  // 提示消息界面配置
  [exports.UIView_Entry.UI_Toast]: {
    prefab: "common/UI_Toast",
    block: false  // 不阻塞交互，允许背景操作
  },

  // 加载界面配置
  [exports.UIView_Entry.UI_Loading]: {
    prefab: "common/UI_Loading",
    block: true  // 阻塞交互，防止加载期间的误操作
  },

  // 调试界面配置
  [exports.UIView_Entry.UI_Debug]: {
    prefab: "common/UI_Debug",
    block: false  // 不阻塞交互，方便调试时的操作
  },

  // 对话框界面配置
  [exports.UIView_Entry.UI_Dialog]: {
    prefab: "common/UI_Dialog",
    block: true  // 阻塞交互，强制用户响应对话框
  },

  // 子游戏界面配置
  [exports.UIView_Entry.UI_Subgame]: {
    prefab: "common/UI_Subgame",
    block: false  // 不阻塞交互，允许返回主界面
  }
};

// 向UI管理器注册入口UI配置
// "resources" 表示这些UI预制体都存放在resources bundle中
UIManager.default.register("resources", exports.UIConfig_Entry);