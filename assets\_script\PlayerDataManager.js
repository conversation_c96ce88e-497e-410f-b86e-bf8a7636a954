/**
 * 玩家数据管理器
 *
 * 功能说明：
 * 1. 管理玩家的所有持久化数据（装备、道具、进度等）
 * 2. 处理玩家数据的存储和读取
 * 3. 管理新手引导进度和状态
 * 4. 处理玩家资源（金币、钻石、体力等）的增减
 * 5. 管理装备系统和主舰系统
 *
 * 设计模式：
 * - 单例模式：全局唯一的数据管理器
 * - 观察者模式：数据变化时通知相关系统
 * - 策略模式：不同类型数据的处理策略
 *
 * 数据结构：
 * - 全局数据：玩家基础信息、资源、装备等
 * - 引导数据：新手引导进度映射
 * - 缓存机制：提高数据访问效率
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_assign = __assign;
const cc_awaiter = __awaiter;
const cc_generator = __generator;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

exports.PlayerDataMgr = undefined;

// 依赖模块导入 - 保持原始的$9前缀以确保兼容性
const $9DataManager = require("DataManager");
const $9EventManager = require("EventManager");
const $9PlatformManager = require("PlatformManager");
const $9TdanalyticsManager = require("TdanalyticsManager");
const $9TipsManager = require("TipsManager");
const $9CommonUtils = require("CommonUtils");
const $9Const_Common = require("Const_Common");
const $9RedPointControl = require("RedPointControl");
const $9Excel = require("Excel");
const $9Logic_GameDataSearch = require("Logic_GameDataSearch");
const $9EvenType = require("EvenType");
const $9GameDataManager = require("GameDataManager");
const $9GameGlobalVariable = require("GameGlobalVariable");

/**
 * 数据存储键名枚举
 * 定义不同类型数据的存储标识符
 */
const DataStorageKeys = {
  /** 全局玩家数据 */
  global: "global",
  /** 引导进度映射数据 */
  guideMap: "guideMap"
};

/**
 * 玩家全局数据结构
 * 包含玩家的所有核心数据
 */
const PlayerGlobalData = function () {
  /** 道具映射表 - 存储各种道具的数量 */
  this.propMap = {};

  /** 装备存储数据 - 存储装备的等级和解锁状态 */
  this.equipSaveData = {};

  /** 已解锁的杂交ID列表 */
  this.unLockZaJiaoIds = [];

  /** 装备穿戴数组 - 8个槽位的装备配置 */
  this.dressUpArray = [0, 0, 0, 0, 0, 0, -1, -1];

  /** 当前主舰ID */
  this.mainShipId = -1;

  /** 主舰存储数据 */
  this.mainShipSaveData = {};

  /** 存档次数计数 */
  this.saveCount = 0;

  /** 下次重置时间戳 */
  this.nextResetTime = 0;

  /** 上次增加体力的时间 */
  this.lastAddPowerTime = 0;

  /** 引导发送索引 */
  this.guideSendIndex = 0;

  /** 玩家开始游戏时间 */
  this.playerStartTime = -1;

  /** 兑换记录 */
  this.duihuanjilu = {};

  /** 购买体力次数 */
  this.addPowerTime = 0;

  /** 视频增加体力次数 */
  this.videoAddPowerTime = 0;

  /** 座舱领取状态 */
  this.zmLinQu = 1;

  /** 分组ID */
  this.fenZuId = 0;
};

/**
 * 装备数据结构
 * 存储单个装备的状态信息
 */
const EquipmentData = function () {
  /** 装备等级 */
  this.level = 0;

  /** 是否已解锁 */
  this.isUnLock = false;
};
/**
 * 玩家数据管理器类
 *
 * 继承自DataManager，提供完整的玩家数据管理功能
 */
const PlayerDataManagerClass = function (BaseDataManager) {

  /**
   * 构造函数
   * 初始化玩家数据管理器的核心属性
   */
  function PlayerDataManager() {
    const instance = BaseDataManager !== null && BaseDataManager.apply(this, arguments) || this;

    /** 数据加密密钥 */
    instance._secret_key = "playerData";

    /** 玩家全局数据实例 */
    instance.globalData = new PlayerGlobalData();

    /** 引导进度映射表 */
    instance.guideMap = {};

    /** 最大体力值 */
    instance.maxPower = 30;

    /** 卡牌解锁章节配置 - 定义每个卡牌槽位的解锁章节 */
    instance.cardUnLockChapter = [0, 0, 0, 0, 0, 0, 2, 4];

    return instance;
  }

  // 设置继承关系
  cc_extends(PlayerDataManager, BaseDataManager);

  // ==================== 初始化方法 ====================

  /**
   * 初始化玩家数据管理器
   *
   * @returns {Promise<void>} 初始化完成的Promise
   *
   * 执行流程：
   * 1. 异步加载玩家全局数据
   * 2. 异步加载引导进度映射数据
   * 3. 合并默认数据和已保存的数据
   * 4. 完成初始化回调
   */
  PlayerDataManager.prototype.init = function () {
    const self = this;

    return new Promise(function (resolve) {
      return cc_awaiter(self, undefined, undefined, function () {
        let savedGlobalData;
        let savedGuideMap;

        return cc_generator(this, function (step) {
          switch (step.label) {
            case 0:
              // 异步加载全局数据
              return [4, this._load(DataStorageKeys.global)];

            case 1:
              savedGlobalData = step.sent();

              // 异步加载引导映射数据
              return [4, this._load(DataStorageKeys.guideMap)];

            case 2:
              savedGuideMap = step.sent();

              // 合并全局数据
              if (savedGlobalData) {
                this.globalData = cc_assign(cc_assign({}, this.globalData), savedGlobalData);
              }

              // 合并引导映射数据
              if (savedGuideMap) {
                this.guideMap = cc_assign(cc_assign({}, this.guideMap), savedGuideMap);
              }

              // 初始化默认数据
              this._initializeDefaultData();

              // 完成初始化
              resolve();

              console.log("PlayerDataManager: 初始化完成");
              return [2];
          }
        });
      });
    });
  };

  /**
   * 初始化默认数据
   * @private
   */
  PlayerDataManager.prototype._initializeDefaultData = function () {
    try {
      // 确保道具映射表存在
      if (!this.globalData.propMap) {
        this.globalData.propMap = {};
      }

      // 确保装备存储数据存在
      if (!this.globalData.equipSaveData) {
        this.globalData.equipSaveData = {};
      }

      // 确保已解锁杂交ID列表存在
      if (!this.globalData.unLockZaJiaoIds) {
        this.globalData.unLockZaJiaoIds = [];
      }

      // 确保装备穿戴数组存在
      if (!this.globalData.dressUpArray) {
        this.globalData.dressUpArray = [0, 0, 0, 0, 0, 0, -1, -1];
      }

      // 确保主舰存储数据存在
      if (!this.globalData.mainShipSaveData) {
        this.globalData.mainShipSaveData = {};
      }

      // 确保兑换记录存在
      if (!this.globalData.duihuanjilu) {
        this.globalData.duihuanjilu = {};
      }

      // 设置默认值
      if (this.globalData.mainShipId === undefined) {
        this.globalData.mainShipId = -1;
      }

      if (this.globalData.saveCount === undefined) {
        this.globalData.saveCount = 0;
      }

      if (this.globalData.playerStartTime === undefined) {
        this.globalData.playerStartTime = -1;
      }

      if (this.globalData.zmLinQu === undefined) {
        this.globalData.zmLinQu = 1;
      }

      if (this.globalData.fenZuId === undefined) {
        this.globalData.fenZuId = 0;
      }

      console.log("PlayerDataManager: 默认数据初始化完成");
    } catch (error) {
      console.error("PlayerDataManager: 初始化默认数据时出错", error);
    }
  };

  // ==================== 属性访问器 ====================

  /**
   * 当前章节属性
   * 根据玩家最大通关等级计算当前章节
   *
   * @returns {number} 当前章节数
   */
  Object.defineProperty(PlayerDataManager.prototype, "chapter", {
    get: function () {
      let maxLevel = 0;
      try {
        if ($9GameDataManager && $9GameDataManager.GameDataMgr && $9GameDataManager.GameDataMgr.getMaxLevel) {
          maxLevel = $9GameDataManager.GameDataMgr.getMaxLevel();
          if (maxLevel < 0) {
            maxLevel = 0;
          }
        } else {
          console.warn("PlayerDataManager: GameDataManager未正确加载，使用默认章节");
        }
      } catch (error) {
        console.error("PlayerDataManager: 获取最大关卡时出错", error);
        maxLevel = 0;
      }
      return maxLevel + 1;
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 分组ID属性
   * 支持多种平台的分组配置
   *
   * @returns {number} 当前分组ID
   */
  Object.defineProperty(PlayerDataManager.prototype, "fenZuId", {
    get: function () {
      try {
        // 头条平台的GM分组配置
        if (window.tt && $9GameGlobalVariable && $9GameGlobalVariable.GameGlobalVariable &&
            $9GameGlobalVariable.GameGlobalVariable.config &&
            $9GameGlobalVariable.GameGlobalVariable.config.gmFenZuId !== -1) {
          return $9GameGlobalVariable.GameGlobalVariable.config.gmFenZuId;
        }

        // 微信平台的实验分组配置（当前被禁用）
        if (false /*window.wx*/) {
          if ($9GameGlobalVariable && $9GameGlobalVariable.GameGlobalVariable &&
              $9GameGlobalVariable.GameGlobalVariable.getWechatExpLevelKey) {
            const wechatExpLevel = $9GameGlobalVariable.GameGlobalVariable.getWechatExpLevelKey();
            if (wechatExpLevel !== -1) {
              return wechatExpLevel;
            }
          }
        }
      } catch (error) {
        console.error("PlayerDataManager: 获取分组ID时出错", error);
      }

      // 返回默认分组ID
      return this.globalData.fenZuId;
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 体力属性
   * 获取玩家当前体力值
   */
  Object.defineProperty(PlayerDataManager.prototype, "power", {
    get: function () {
      return this.globalData.power || 100;
    },
    set: function (value) {
      this.globalData.power = value;
      this.saveGlobalData();
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 最大体力属性
   * 获取玩家最大体力值
   */
  Object.defineProperty(PlayerDataManager.prototype, "maxPower", {
    get: function () {
      return this.globalData.maxPower || 100;
    },
    set: function (value) {
      this.globalData.maxPower = value;
      this.saveGlobalData();
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 金币属性
   * 获取玩家当前金币数量
   */
  Object.defineProperty(PlayerDataManager.prototype, "gold", {
    get: function () {
      return this.globalData.gold || 0;
    },
    set: function (value) {
      this.globalData.gold = value;
      this.saveGlobalData();
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 钻石属性
   * 获取玩家当前钻石数量
   */
  Object.defineProperty(PlayerDataManager.prototype, "diamond", {
    get: function () {
      return this.globalData.diamond || 0;
    },
    set: function (value) {
      this.globalData.diamond = value;
      this.saveGlobalData();
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 广告券属性
   * 获取玩家当前广告券数量
   */
  Object.defineProperty(PlayerDataManager.prototype, "advoucher", {
    get: function () {
      return this.globalData.advoucher || 0;
    },
    set: function (value) {
      this.globalData.advoucher = value;
      this.saveGlobalData();
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 装备穿戴数组属性
   * 获取玩家当前装备穿戴状态
   */
  Object.defineProperty(PlayerDataManager.prototype, "dressUpArray", {
    get: function () {
      return this.globalData.dressUpArray || [0, 0, 0, 0, 0, 0, -1, -1];
    },
    set: function (value) {
      this.globalData.dressUpArray = value;
      this.saveGlobalData();
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 最后添加体力时间属性
   * 获取最后一次添加体力的时间戳
   */
  Object.defineProperty(PlayerDataManager.prototype, "lastAddPowerTime", {
    get: function () {
      return this.globalData.lastAddPowerTime || Date.now();
    },
    set: function (value) {
      this.globalData.lastAddPowerTime = value;
      this.saveGlobalData();
    },
    enumerable: false,
    configurable: true
  });

  // ==================== 方法定义 ====================

  /**
   * 设置分组ID
   *
   * @param {number} groupId - 分组ID（0-4之间的有效值）
   */
  PlayerDataManager.prototype.SetFenZuId = function (groupId) {
    if (groupId >= 0 && groupId < 5) {
      this.globalData.fenZuId = groupId;
    }
  };

  /**
   * 保存引导进度映射数据
   */
  PlayerDataManager.prototype.SaveGuideMap = function () {
    this._save(DataStorageKeys.guideMap, this.guideMap);
  };

  /**
   * 根据任务名称获取引导进度索引
   *
   * @param {string} taskName - 引导任务名称
   * @returns {number} 引导进度索引
   */
  PlayerDataManager.prototype.GetGuideIndexByTaskName = function (taskName) {
    if (!this.guideMap[taskName]) {
      this.guideMap[taskName] = 0;
    }
    return this.guideMap[taskName];
  };

  /**
   * 设置引导进度索引
   *
   * @param {string} guideName - 引导名称
   * @param {number} guideIndex - 引导索引
   * @param {boolean} skipAnalytics - 是否跳过数据统计（默认false）
   */
  PlayerDataManager.prototype.SetGuideIndex = function (guideName, guideIndex, skipAnalytics) {
    // 修复原代码中的错误赋值
    if (skipAnalytics === undefined) {
      skipAnalytics = false;
    }

    // 初始化引导映射
    if (!this.guideMap[guideName]) {
      this.guideMap[guideName] = 0;
    }

    // 只有当新索引大于当前索引时才更新
    if (guideIndex > this.guideMap[guideName]) {
      this.guideMap[guideName] = guideIndex;

      // 特殊处理：战斗背包引导不是完结状态时才保存
      if (guideName !== $9Const_Common.GuideName.战斗背包 ||
          guideIndex !== $9Const_Common.GameBagGuideIndex.引导完结) {
        this.SaveGuideMap();
      }

      // 构建引导步骤描述
      let guideDisplayName = guideName;
      let stepDescription = String(guideIndex);

      switch (guideName) {
        case $9Const_Common.GuideName.战斗背包:
          guideDisplayName = "战斗背包";
          stepDescription = [
            "引导初始1", "引导拖动武器2", "引导刷新按钮3", "引导合成4",
            "引导开战5", "引导词条6", "引导词条6完结", "引导格子7",
            "引导其他水果8", "引导结束9", "引导完结"
          ][guideIndex];
          break;

        case $9Const_Common.GuideName.武器升级:
          guideDisplayName = "武器升级";
          stepDescription = [
            "给固定奖励1", "引导点击仓库2", "引导点击升级3",
            "引导杂交解锁4", "引导战斗5", "引导完结"
          ][guideIndex];
          break;

        case $9Const_Common.GuideName.武器上阵:
          guideDisplayName = "武器上阵";
          stepDescription = [
            "引导对话", "选择武器", "引导穿戴", "引导完结"
          ][guideIndex];
          break;
      }

      const analyticsEventName = guideDisplayName + "_" + stepDescription;

      // 防止重复发送战斗背包引导数据
      if (guideName === $9Const_Common.GuideName.战斗背包 &&
          guideIndex <= this.globalData.guideSendIndex) {
        return;
      }

      // 更新引导发送索引
      this.globalData.guideSendIndex = guideIndex;

      // 发送平台统计事件（添加安全检查）
      try {
        if ($9PlatformManager && $9PlatformManager.PlatformMgr && $9PlatformManager.PlatformMgr.trackEvent) {
          $9PlatformManager.PlatformMgr.trackEvent(
            $9Const_Common.UMAPoint.新手引导打点,
            analyticsEventName
          );
        } else {
          console.warn("PlayerDataManager: PlatformManager未正确加载，跳过统计事件");
        }
      } catch (error) {
        console.error("PlayerDataManager: 发送平台统计事件时出错", error);
      }

      // 发送TD Analytics统计（除非跳过）
      if (!skipAnalytics) {
        try {
          if ($9TdanalyticsManager && $9TdanalyticsManager.TdanalyticsMgr && $9TdanalyticsManager.TdanalyticsMgr.track_guideStep) {
            $9TdanalyticsManager.TdanalyticsMgr.track_guideStep(
              guideDisplayName,
              stepDescription
            );
          } else {
            console.warn("PlayerDataManager: TdanalyticsManager未正确加载，跳过统计事件");
          }
        } catch (error) {
          console.error("PlayerDataManager: 发送TD Analytics统计时出错", error);
        }
      }
    }
  };

  /**
   * GM命令：设置引导进度索引
   *
   * @param {string} guideName - 引导名称
   * @param {number} guideIndex - 引导索引
   */
  PlayerDataManager.prototype.GMSetGuideIndex = function (guideName, guideIndex) {
    this.guideMap[guideName] = guideIndex;
    this.SaveGuideMap();
  };

  /**
   * 根据道具ID获取道具数量
   *
   * @param {number} propId - 道具ID
   * @returns {number} 道具数量
   */
  PlayerDataManager.prototype.GetPropById = function (propId) {
    if (!this.globalData.propMap) {
      this.globalData.propMap = {};
    }
    return this.globalData.propMap[propId] || 0;
  };

  /**
   * 设置道具数量
   *
   * @param {number} propId - 道具ID
   * @param {number} count - 道具数量
   */
  PlayerDataManager.prototype.SetPropById = function (propId, count) {
    if (!this.globalData.propMap) {
      this.globalData.propMap = {};
    }
    this.globalData.propMap[propId] = count;
    this.saveGlobalData();
  };

  /**
   * 游戏胜利检查解锁
   *
   * @returns {Array} 解锁的内容数组
   */
  PlayerDataManager.prototype.GameWinCheckUnlock = function () {
    // 这个方法的具体实现需要根据游戏逻辑来定义
    // 暂时返回空数组，避免错误
    return this.globalData.gameWinUnlocks || [];
  };

  /**
   * 添加体力
   *
   * @param {number} amount - 要添加的体力数量
   */
  PlayerDataManager.prototype.AddPower = function (amount) {
    if (amount <= 0) {
      console.warn("PlayerDataManager: 添加体力数量必须大于0", amount);
      return;
    }

    const currentPower = this.power;
    const maxPower = this.maxPower;
    const newPower = Math.min(currentPower + amount, maxPower);

    this.power = newPower;
    console.log(`PlayerDataManager: 添加体力 ${amount}，当前体力: ${newPower}/${maxPower}`);
  };

  /**
   * 扣除广告券
   *
   * @param {number} amount - 要扣除的广告券数量
   * @returns {boolean} 扣除是否成功
   */
  PlayerDataManager.prototype.SubAdvoucher = function (amount) {
    if (amount <= 0) {
      console.warn("PlayerDataManager: 扣除广告券数量必须大于0", amount);
      return false;
    }

    const currentAdvoucher = this.advoucher;
    if (currentAdvoucher < amount) {
      console.warn("PlayerDataManager: 广告券不足", currentAdvoucher, amount);
      return false;
    }

    this.advoucher = currentAdvoucher - amount;
    console.log(`PlayerDataManager: 扣除广告券 ${amount}，当前广告券: ${this.advoucher}`);
    return true;
  };

  /**
   * 保存全局数据
   */
  PlayerDataManager.prototype.saveGlobalData = function () {
    this._save(DataStorageKeys.global, this.globalData);
  };

  // 返回玩家数据管理器类
  return PlayerDataManager;

}($9DataManager.default);

// 导出玩家数据管理器单例实例
exports.PlayerDataMgr = PlayerDataManagerClass.getInstance();