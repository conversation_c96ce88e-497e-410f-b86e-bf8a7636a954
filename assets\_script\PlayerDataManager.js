/**
 * 玩家数据管理器
 *
 * 功能说明：
 * 1. 管理玩家的所有持久化数据（装备、道具、进度等）
 * 2. 处理玩家数据的存储和读取
 * 3. 管理新手引导进度和状态
 * 4. 处理玩家资源（金币、钻石、体力等）的增减
 * 5. 管理装备系统和主舰系统
 *
 * 设计模式：
 * - 单例模式：全局唯一的数据管理器
 * - 观察者模式：数据变化时通知相关系统
 * - 策略模式：不同类型数据的处理策略
 *
 * 数据结构：
 * - 全局数据：玩家基础信息、资源、装备等
 * - 引导数据：新手引导进度映射
 * - 缓存机制：提高数据访问效率
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_assign = __assign;
const cc_awaiter = __awaiter;
const cc_generator = __generator;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

exports.PlayerDataMgr = undefined;

// 依赖模块导入
const DataManager = require("DataManager");
const EventManager = require("EventManager");
const PlatformManager = require("PlatformManager");
const TdanalyticsManager = require("TdanalyticsManager");
const TipsManager = require("TipsManager");
const CommonUtils = require("CommonUtils");
const ConstCommon = require("Const_Common");
const RedPointControl = require("RedPointControl");
const Excel = require("Excel");
const LogicGameDataSearch = require("Logic_GameDataSearch");
const EventTypes = require("EvenType");
const GameDataManager = require("GameDataManager");
const GameGlobalVariable = require("GameGlobalVariable");

/**
 * 数据存储键名枚举
 * 定义不同类型数据的存储标识符
 */
const DataStorageKeys = {
  /** 全局玩家数据 */
  global: "global",
  /** 引导进度映射数据 */
  guideMap: "guideMap"
};

/**
 * 玩家全局数据结构
 * 包含玩家的所有核心数据
 */
const PlayerGlobalData = function () {
  /** 道具映射表 - 存储各种道具的数量 */
  this.propMap = {};

  /** 装备存储数据 - 存储装备的等级和解锁状态 */
  this.equipSaveData = {};

  /** 已解锁的杂交ID列表 */
  this.unLockZaJiaoIds = [];

  /** 装备穿戴数组 - 8个槽位的装备配置 */
  this.dressUpArray = [0, 0, 0, 0, 0, 0, -1, -1];

  /** 当前主舰ID */
  this.mainShipId = -1;

  /** 主舰存储数据 */
  this.mainShipSaveData = {};

  /** 存档次数计数 */
  this.saveCount = 0;

  /** 下次重置时间戳 */
  this.nextResetTime = 0;

  /** 上次增加体力的时间 */
  this.lastAddPowerTime = 0;

  /** 引导发送索引 */
  this.guideSendIndex = 0;

  /** 玩家开始游戏时间 */
  this.playerStartTime = -1;

  /** 兑换记录 */
  this.duihuanjilu = {};

  /** 购买体力次数 */
  this.addPowerTime = 0;

  /** 视频增加体力次数 */
  this.videoAddPowerTime = 0;

  /** 座舱领取状态 */
  this.zmLinQu = 1;

  /** 分组ID */
  this.fenZuId = 0;
};

/**
 * 装备数据结构
 * 存储单个装备的状态信息
 */
const EquipmentData = function () {
  /** 装备等级 */
  this.level = 0;

  /** 是否已解锁 */
  this.isUnLock = false;
};
/**
 * 玩家数据管理器类
 *
 * 继承自DataManager，提供完整的玩家数据管理功能
 */
const PlayerDataManagerClass = function (BaseDataManager) {

  /**
   * 构造函数
   * 初始化玩家数据管理器的核心属性
   */
  function PlayerDataManager() {
    const instance = BaseDataManager !== null && BaseDataManager.apply(this, arguments) || this;

    /** 数据加密密钥 */
    instance._secret_key = "playerData";

    /** 玩家全局数据实例 */
    instance.globalData = new PlayerGlobalData();

    /** 引导进度映射表 */
    instance.guideMap = {};

    /** 最大体力值 */
    instance.maxPower = 30;

    /** 卡牌解锁章节配置 - 定义每个卡牌槽位的解锁章节 */
    instance.cardUnLockChapter = [0, 0, 0, 0, 0, 0, 2, 4];

    return instance;
  }

  // 设置继承关系
  cc_extends(PlayerDataManager, BaseDataManager);

  /**
   * 当前章节属性
   * 根据玩家最大通关等级计算当前章节
   *
   * @returns {number} 当前章节数
   */
  Object.defineProperty(PlayerDataManager.prototype, "chapter", {
    get: function () {
      let maxLevel = GameDataManager.GameDataMgr.getMaxLevel();
      if (maxLevel < 0) {
        maxLevel = 0;
      }
      return maxLevel + 1;
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 分组ID属性
   * 支持多种平台的分组配置
   *
   * @returns {number} 当前分组ID
   */
  Object.defineProperty(PlayerDataManager.prototype, "fenZuId", {
    get: function () {
      // 头条平台的GM分组配置
      if (window.tt && GameGlobalVariable.GameGlobalVariable.config.gmFenZuId !== -1) {
        return GameGlobalVariable.GameGlobalVariable.config.gmFenZuId;
      }

      // 微信平台的实验分组配置（当前被禁用）
      if (false /*window.wx*/) {
        const wechatExpLevel = GameGlobalVariable.GameGlobalVariable.getWechatExpLevelKey();
        if (wechatExpLevel !== -1) {
          return wechatExpLevel;
        }
      }

      // 返回默认分组ID
      return this.globalData.fenZuId;
    },
    enumerable: false,
    configurable: true
  });
  /**
   * 设置分组ID
   *
   * @param {number} groupId - 分组ID（0-4之间的有效值）
   */
  PlayerDataManager.prototype.SetFenZuId = function (groupId) {
    if (groupId >= 0 && groupId < 5) {
      this.globalData.fenZuId = groupId;
    }
  };

  /**
   * 保存引导进度映射数据
   */
  PlayerDataManager.prototype.SaveGuideMap = function () {
    this._save(DataStorageKeys.guideMap, this.guideMap);
  };

  /**
   * 根据任务名称获取引导进度索引
   *
   * @param {string} taskName - 引导任务名称
   * @returns {number} 引导进度索引
   */
  PlayerDataManager.prototype.GetGuideIndexByTaskName = function (taskName) {
    if (!this.guideMap[taskName]) {
      this.guideMap[taskName] = 0;
    }
    return this.guideMap[taskName];
  };

  /**
   * 设置引导进度索引
   *
   * @param {string} guideName - 引导名称
   * @param {number} guideIndex - 引导索引
   * @param {boolean} skipAnalytics - 是否跳过数据统计（默认false）
   */
  PlayerDataManager.prototype.SetGuideIndex = function (guideName, guideIndex, skipAnalytics) {
    // 修复原代码中的错误赋值
    if (skipAnalytics === undefined) {
      skipAnalytics = false;
    }

    // 初始化引导映射
    if (!this.guideMap[guideName]) {
      this.guideMap[guideName] = 0;
    }

    // 只有当新索引大于当前索引时才更新
    if (guideIndex > this.guideMap[guideName]) {
      this.guideMap[guideName] = guideIndex;

      // 特殊处理：战斗背包引导不是完结状态时才保存
      if (guideName !== ConstCommon.GuideName.战斗背包 ||
          guideIndex !== ConstCommon.GameBagGuideIndex.引导完结) {
        this.SaveGuideMap();
      }

      // 构建引导步骤描述
      let guideDisplayName = guideName;
      let stepDescription = String(guideIndex);

      switch (guideName) {
        case ConstCommon.GuideName.战斗背包:
          guideDisplayName = "战斗背包";
          stepDescription = [
            "引导初始1", "引导拖动武器2", "引导刷新按钮3", "引导合成4",
            "引导开战5", "引导词条6", "引导词条6完结", "引导格子7",
            "引导其他水果8", "引导结束9", "引导完结"
          ][guideIndex];
          break;

        case ConstCommon.GuideName.武器升级:
          guideDisplayName = "武器升级";
          stepDescription = [
            "给固定奖励1", "引导点击仓库2", "引导点击升级3",
            "引导杂交解锁4", "引导战斗5", "引导完结"
          ][guideIndex];
          break;

        case ConstCommon.GuideName.武器上阵:
          guideDisplayName = "武器上阵";
          stepDescription = [
            "引导对话", "选择武器", "引导穿戴", "引导完结"
          ][guideIndex];
          break;
      }

      const analyticsEventName = guideDisplayName + "_" + stepDescription;

      // 防止重复发送战斗背包引导数据
      if (guideName === ConstCommon.GuideName.战斗背包 &&
          guideIndex <= this.globalData.guideSendIndex) {
        return;
      }

      // 更新引导发送索引
      this.globalData.guideSendIndex = guideIndex;

      // 发送平台统计事件
      PlatformManager.PlatformMgr.trackEvent(
        ConstCommon.UMAPoint.新手引导打点,
        analyticsEventName
      );

      // 发送TD Analytics统计（除非跳过）
      if (!skipAnalytics) {
        TdanalyticsManager.TdanalyticsMgr.track_guideStep(
          guideDisplayName,
          stepDescription
        );
      }
    }
  };

  /**
   * GM命令：设置引导进度索引
   *
   * @param {string} guideName - 引导名称
   * @param {number} guideIndex - 引导索引
   */
  PlayerDataManager.prototype.GMSetGuideIndex = function (guideName, guideIndex) {
    this.guideMap[guideName] = guideIndex;
    this.SaveGuideMap();
  };
  t.prototype.GetPropById = function (e) {
    var t = this.globalData.propMap[e];
    t || (t = 0);
    return t;
  };
  t.prototype.UsePropById = function (e, t) {
    var o = this.globalData.propMap[e];
    return !(!o || o < t || (this.globalData.propMap[e] -= t, this.SaveGlobalData(), $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.HallResetPropCount), 0));
  };
  t.prototype.AddPropById = function (e, t) {
    this.globalData.propMap[e] || (this.globalData.propMap[e] = 0);
    this.globalData.propMap[e] += t;
    this.SaveGlobalData();
    $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.HallResetPropCount);
    this.ChangeRich();
    e <= $9Const_Common.PropId.金币 && $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.MainRedPointChange, $9RedPointControl.RedPointType.upEquip);
  };
  t.prototype.init = function () {
    var e = this;
    return new Promise(function (t) {
      return cc__awaiter(e, undefined, undefined, function () {
        var e;
        var o;
        return cc__generator(this, function (n) {
          switch (n.label) {
            case 0:
              this.globalData = new M();
              return [4, this._load(s.global)];
            case 1:
              e = n.sent();
              this.globalData = cc__assign(cc__assign({}, this.globalData), e);
              return [4, this._load(s.guideMap)];
            case 2:
              o = n.sent();
              this.guideMap = cc__assign(cc__assign({}, this.guideMap), o);
              this.StartCheckEquip();
              this.CheckIsTimeToReset();
              e || this.PlayerFirstTime();
              this.OtherCheck();
              t();
              return [2];
          }
        });
      });
    });
  };
  t.prototype.OtherCheck = function () {
    var e = this.cardUnLockChapter;
    for (var t = 0; t < e.length; t++) {
      if (t > this.globalData.dressUpArray.length) {
        this.globalData.dressUpArray.push(-1);
      } else if (this.chapter <= e[t]) {
        this.globalData.dressUpArray[t] = -1;
      } else {
        this.chapter > e[t] && -1 == this.globalData.dressUpArray[t] && (this.globalData.dressUpArray[t] = 0);
      }
    }
  };
  t.prototype.CheckIsTimeToReset = function () {
    var e = $9CommonUtils.default.GetCurrTime();
    var t = e.getTime();
    if (!this.globalData.nextResetTime || t >= this.globalData.nextResetTime) {
      e.setHours(24, 0, 0, 0);
      this.globalData.nextResetTime = e.getTime();
      this.ResetAllByTime();
    }
  };
  t.prototype.ResetAllByTime = function () {
    this.globalData.addPowerTime = 0;
    this.globalData.videoAddPowerTime = 0;
    this.SaveGlobalData();
  };
  t.prototype.SaveGlobalData = function () {
    this._save(s.global, this.globalData);
  };
  Object.defineProperty(t.prototype, "power", {
    get: function () {
      return this.GetPropById($9Const_Common.PropId.体力);
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.AddPower = function (e) {
    this.AddPropById($9Const_Common.PropId.体力, e);
    this.ChangeRich();
  };
  t.prototype.SubPower = function (e) {
    if (this.power < e) {
      return $9TipsManager.TipMgr.showToast("体力不足"), false;
    } else {
      return this.power >= this.maxPower && this.power - e < this.maxPower && (this.globalData.lastAddPowerTime = $9CommonUtils.default.GetCurrTimeCount()), this.UsePropById($9Const_Common.PropId.体力, e), this.ChangeRich(), true;
    }
  };
  Object.defineProperty(t.prototype, "lastAddPowerTime", {
    get: function () {
      return this.globalData.lastAddPowerTime;
    },
    set: function (e) {
      this.globalData.lastAddPowerTime = e;
    },
    enumerable: false,
    configurable: true
  });
  Object.defineProperty(t.prototype, "gold", {
    get: function () {
      return this.GetPropById($9Const_Common.PropId.金币);
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.AddGold = function (e) {
    this.AddPropById($9Const_Common.PropId.金币, e);
    this.ChangeRich();
  };
  t.prototype.SubGold = function (e) {
    if (this.gold < e) {
      return $9TipsManager.TipMgr.showToast("金币不足"), false;
    } else {
      return this.UsePropById($9Const_Common.PropId.金币, e), this.ChangeRich(), $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.MainRedPointChange, $9RedPointControl.RedPointType.upEquip), true;
    }
  };
  Object.defineProperty(t.prototype, "diamond", {
    get: function () {
      return this.GetPropById($9Const_Common.PropId.钻石);
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.AddDiamond = function (e) {
    this.AddPropById($9Const_Common.PropId.钻石, e);
    this.ChangeRich();
  };
  t.prototype.SubDiamond = function (e) {
    if (this.diamond < e) {
      return $9TipsManager.TipMgr.showToast("钻石不足"), false;
    } else {
      return this.UsePropById($9Const_Common.PropId.钻石, e), this.ChangeRich(), true;
    }
  };
  Object.defineProperty(t.prototype, "advoucher", {
    get: function () {
      return this.GetPropById($9Const_Common.PropId.战斗免广卡);
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.AddAdvoucher = function (e) {
    this.AddPropById($9Const_Common.PropId.战斗免广卡, e);
    this.ChangeRich();
  };
  t.prototype.SubAdvoucher = function (e) {
    return !(this.advoucher < e || (this.UsePropById($9Const_Common.PropId.战斗免广卡, e), this.ChangeRich(), 0));
  };
  t.prototype.ChangeRich = function () {
    $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.Hall_ResetRich);
  };
  Object.defineProperty(t.prototype, "addPowerTime", {
    get: function () {
      return this.globalData.addPowerTime;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.AddBuyPowerTime = function (e) {
    this.globalData.addPowerTime += e;
  };
  Object.defineProperty(t.prototype, "videoAddPowerTime", {
    get: function () {
      return this.globalData.videoAddPowerTime;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.AddVideoAddPowerTime = function (e) {
    this.globalData.videoAddPowerTime += e;
  };
  t.prototype.PlayerFirstTime = function () {
    var e = this;
    $9Logic_GameDataSearch.default.getHaiZhanWuQi_All($9Const_Common.ShipType.武器).forEach(function (t) {
      var o = $9CommonUtils.default.GetWuQiUnLock(t);
      -1 != t.isShow && o >= 0 && o < e.chapter && e.AddDressUpEquip(t.id);
    });
    var t = $9Logic_GameDataSearch.default.getHaiZhanWuQi_All($9Const_Common.ShipType.主舰);
    -1 != this.globalData.mainShipId && t.has(this.globalData.mainShipId) || t.forEach(function (o) {
      var n = $9CommonUtils.default.GetWuQiUnLock(o);
      (-1 == e.globalData.mainShipId || !t.has(e.globalData.mainShipId)) && n < e.chapter && (e.mainShipId = o.id);
    });
    this.AddPower(30);
  };
  t.prototype.StartCheckEquip = function () {
    var e = this;
    var t = false;
    $9Logic_GameDataSearch.default.getHaiZhanWuQi_All($9Const_Common.ShipType.武器).forEach(function (o) {
      if (!e.globalData.equipSaveData[o.id]) {
        e.globalData.equipSaveData[o.id] = new P();
        t = true;
      }
      var n = $9CommonUtils.default.GetWuQiUnLock(o);
      -1 != n && n >= 0 && !e.globalData.equipSaveData[o.id].isUnLock && (e.globalData.equipSaveData[o.id].isUnLock = e.chapter > n);
    });
    if (-1 == this.globalData.playerStartTime) {
      this.globalData.playerStartTime = $9CommonUtils.default.GetCurrTimeCount();
      t = true;
    }
    t && this.SaveGlobalData();
  };
  t.prototype.GameWinCheckUnlock = function () {
    var e = this;
    var t = [];
    var o = false;
    var n = this.chapter;
    $9Logic_GameDataSearch.default.getHaiZhanWuQi_All($9Const_Common.ShipType.武器).forEach(function (i) {
      var a = $9CommonUtils.default.GetWuQiUnLock(i);
      if (-1 != a && a >= 0 && !e.globalData.equipSaveData[i.id].isUnLock && n > a) {
        e.globalData.equipSaveData[i.id].isUnLock = true;
        o = true;
        t.push(i.id);
      }
    });
    if (o) {
      this.SaveGlobalData();
      this.OtherCheck();
    }
    return t;
  };
  t.prototype.AddWeapon = function (e) {
    this.globalData.equipSaveData[e] || (this.globalData.equipSaveData[e] = new P());
    this.globalData.equipSaveData[e].isUnLock = true;
    this.SaveGlobalData();
  };
  Object.defineProperty(t.prototype, "dressUpArray", {
    get: function () {
      return this.globalData.dressUpArray;
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.HallGetEquipIsDressUp = function (e) {
    return !!$9Excel.Excel.haizhanwuqi(e) && -1 != this.dressUpArray.indexOf(e);
  };
  t.prototype.GetEquipIsDressUp = function (e) {
    var t = $9Excel.Excel.haizhanwuqi(e);
    return !t || -1 != this.dressUpArray.indexOf(e) || -1 == t.isShow;
  };
  t.prototype.AddDressUpEquip = function (e, t) {
    undefined === t && (t = -1);
    if (-1 == t) {
      for (var o = 0; o < this.globalData.dressUpArray.length; o++) {
        if (0 == this.globalData.dressUpArray[o]) {
          t = o;
          break;
        }
      }
      if (-1 == t) {
        return;
      }
    }
    this.globalData.dressUpArray[t] = e;
    this.SaveGlobalData();
    $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.MainRedPointChange, $9RedPointControl.RedPointType.upEquip);
  };
  t.prototype.RemoveDressUpEquip = function (e, t) {
    undefined === t && (t = -1);
    if (-1 == t) {
      for (var o = 0; o < this.globalData.dressUpArray.length; o++) {
        if (this.globalData.dressUpArray[o] == e) {
          t = o;
          break;
        }
      }
      if (-1 == t) {
        return;
      }
    }
    this.globalData.dressUpArray[t] = 0;
    this.SaveGlobalData();
    $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.MainRedPointChange, $9RedPointControl.RedPointType.upEquip);
  };
  Object.defineProperty(t.prototype, "mainShipId", {
    get: function () {
      return this.globalData.mainShipId;
    },
    set: function (e) {
      if ($9Logic_GameDataSearch.default.getHaiZhanWuQi_All($9Const_Common.ShipType.主舰).has(e)) {
        this.globalData.mainShipId = e;
        this.SaveGlobalData();
      } else {
        $9TipsManager.TipMgr.showToast("这艘主舰不存在");
      }
    },
    enumerable: false,
    configurable: true
  });
  t.prototype.AddEquipLevel = function (e) {
    if (this.globalData.equipSaveData[e] && this.globalData.equipSaveData[e].isUnLock) {
      this.globalData.equipSaveData[e].level++;
      this.SaveGlobalData();
    }
  };
  t.prototype.AddEquipFragments = function (e, t) {
    this.AddPropById(e, t);
    $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.MainRedPointChange, $9RedPointControl.RedPointType.upEquip);
  };
  t.prototype.UseEquipFragments = function (e, t) {
    if (this.GetPropById(e) < t) {
      return $9TipsManager.TipMgr.showToast("碎片不足"), false;
    } else {
      return this.UsePropById(e, t), true;
    }
  };
  t.prototype.GetEquipDataById = function (e) {
    return this.globalData.equipSaveData[e];
  };
  t.prototype.GetMainShipById = function (e) {
    this.globalData.mainShipSaveData[e] || (this.globalData.mainShipSaveData[e] = new $9Const_Common.MainShipSaveData());
    this.globalData.mainShipSaveData[e].videoCount || (this.globalData.mainShipSaveData[e].videoCount = 0);
    return this.globalData.mainShipSaveData[e];
  };
  t.prototype.AddMainShipVideoById = function (e, t) {
    this.globalData.mainShipSaveData[e] || (this.globalData.mainShipSaveData[e] = new $9Const_Common.MainShipSaveData());
    this.globalData.mainShipSaveData[e].videoCount || (this.globalData.mainShipSaveData[e].videoCount = 0);
    this.globalData.mainShipSaveData[e].videoCount += t;
    this.SaveGlobalData();
  };
  t.prototype.AddMainShipLevelById = function (e, t) {
    this.globalData.mainShipSaveData[e] || (this.globalData.mainShipSaveData[e] = new $9Const_Common.MainShipSaveData());
    this.globalData.mainShipSaveData[e].level += t;
    this.SaveGlobalData();
  };
  t.prototype.AddMainShipStarById = function (e, t) {
    this.globalData.mainShipSaveData[e] || (this.globalData.mainShipSaveData[e] = new $9Const_Common.MainShipSaveData());
    this.globalData.mainShipSaveData[e].star += t;
    this.SaveGlobalData();
  };
  t.prototype.resetData = function () {
    this._clear();
  };
  t.prototype.getServerTime = function () {
    return $9CommonUtils.default.GetCurrTime();
  };
  t.prototype.isDuiHuan = function (e) {
    return this.globalData.duihuanjilu[e];
  };
  t.prototype.saveDuiHuanJiLu = function (e) {
    this.globalData.duihuanjilu[e] = "ok";
    this.SaveGlobalData();
  };
  t.prototype.GetZaJiaoIsUnlock = function (e) {
    return -1 != this.globalData.unLockZaJiaoIds.indexOf(e);
  };
  t.prototype.SetZaJiaoUnlock = function (e) {
    -1 == this.globalData.unLockZaJiaoIds.indexOf(e) && this.globalData.unLockZaJiaoIds.push(e);
    this.SaveGlobalData();
  };
  t.prototype.saveZuoMianJiLu = function () {
    this.globalData.zmLinQu = 2;
  };
  Object.defineProperty(t.prototype, "zmLinQu", {
    get: function () {
      return this.globalData.zmLinQu;
    },
    enumerable: false,
    configurable: true
  });
  return t;
}($9DataManager.default);
exports.PlayerDataMgr = S.getInstance();