var n;
var cc__extends = __extends;
var cc__awaiter = __awaiter;
var cc__generator = __generator;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var $9UserData = require("UserData");
var $9YPNetStorage = require("YPNetStorage");
var $9GameConfig = require("GameConfig");
var $9LogManager = require("LogManager");
var $9PlatformManager = require("PlatformManager");
var $1$9SingletonManager = require("SingletonManager");
var def_DataManager = function (e) {
  function _ctor() {
    var t = null !== e && e.apply(this, arguments) || this;
    t._secret_key = "secret-key";
    t._dataMap = new Map();
    t._isUpload = true;
    t._isClear = false;
    return t;
  }
  cc__extends(_ctor, e);
  _ctor.prototype._save = function (e, t) {
    var o = JSON.stringify(t);
    var n = this._getKey(e);
    this._setData(n, o);
    this._dataMap.set(e, t);
    this._hasRomete() && $9YPNetStorage.default.setStorage(this._secret_key, e, o);
  };
  _ctor.prototype._load = function (e) {
    return cc__awaiter(this, undefined, undefined, function () {
      var t = this;
      return cc__generator(this, function () {
        return [2, new Promise(function (o) {
          return cc__awaiter(t, undefined, undefined, function () {
            var t;
            var n;
            var i;
            return cc__generator(this, function (a) {
              switch (a.label) {
                case 0:
                  t = this._getKey(e);
                  return [4, this._getData(t)];
                case 1:
                  n = a.sent();
                  i = null;
                  if (n) {
                    try {
                      i = JSON.parse(n);
                    } catch (e) {
                      i = n;
                    }
                  }
                  this._dataMap.set(e, i);
                  this._hasRomete() && $9YPNetStorage.default.setStorage(this._secret_key, e, n);
                  o(i);
                  return [2];
              }
            });
          });
        })];
      });
    });
  };
  _ctor.prototype._clear = function (e) {
    var t = [];
    if (e) {
      t.push(e);
    } else {
      t = Array.from(this._dataMap.keys());
    }
    for (var o = 0; o < t.length; o++) {
      var n = t[o];
      var i = this._getKey(n);
      this._cleanData(i);
    }
  };
  _ctor.prototype.clearSync = function () {
    var e = this;
    return new Promise(function (t) {
      return cc__awaiter(e, undefined, undefined, function () {
        var e;
        var o;
        var n;
        var i;
        return cc__generator(this, function (a) {
          switch (a.label) {
            case 0:
              this._isClear = true;
              e = Array.from(this._dataMap.keys());
              o = 0;
              a.label = 1;
            case 1:
              if (o < e.length) {
                return n = e[o], i = this._getKey(n), [4, this._cleanData(i)];
              } else {
                return [3, 4];
              }
            case 2:
              a.sent();
              $9LogManager.LogMgr.info("清档", i);
              a.label = 3;
            case 3:
              o++;
              return [3, 1];
            case 4:
              t(true);
              return [2];
          }
        });
      });
    });
  };
  _ctor.prototype._getKey = function (e) {
    try {
      // 安全检查GameConfig和appConfig是否存在
      if ($9GameConfig && $9GameConfig.default && $9GameConfig.default.appConfig && $9GameConfig.default.appConfig.Publish) {
        return this._secret_key + "-release-" + e;
      } else {
        // 安全检查UserData是否存在
        const userCode = ($9UserData && $9UserData.User && $9UserData.User.code) ? $9UserData.User.code : "default";
        return this._secret_key + "-debug-" + e + "-" + userCode;
      }
    } catch (error) {
      console.error("DataManager: _getKey方法出错", error);
      // 提供一个安全的默认键名
      return this._secret_key + "-safe-" + e;
    }
  };
  _ctor.prototype.isNull = function (e) {
    return null == e || null == e;
  };
  _ctor.prototype._getData = function (e) {
    var t = this;
    return new Promise(function (o) {
      return cc__awaiter(t, undefined, undefined, function () {
        var t;
        return cc__generator(this, function (n) {
          switch (n.label) {
            case 0:
              t = cc.sys.localStorage.getItem(e);
              if (this._isEmpyt(t)) {
                return [3, 1];
              } else {
                return o(t), [3, 4];
              }
            case 1:
              if (this._hasRomete()) {
                return [4, this._getRomete(e)];
              } else {
                return [3, 3];
              }
            case 2:
              t = n.sent();
              $9LogManager.LogMgr.info("云存档", e, t, this._isEmpyt(t));
              if (this._isEmpyt(t)) {
                o(null);
              } else {
                o(t);
              }
              return [3, 4];
            case 3:
              o(null);
              n.label = 4;
            case 4:
              return [2];
          }
        });
      });
    });
  };
  _ctor.prototype._setData = function (e, t) {
    cc.sys.localStorage.setItem(e, t);
    this._hasRomete() && this._setRomete(e, t);
  };
  _ctor.prototype._cleanData = function (e) {
    var t = this;
    return new Promise(function (o) {
      return cc__awaiter(t, undefined, undefined, function () {
        return cc__generator(this, function (t) {
          switch (t.label) {
            case 0:
              cc.sys.localStorage.removeItem(e);
              if (this._hasRomete()) {
                return [4, this._setRomete(e, "")];
              } else {
                return [3, 2];
              }
            case 1:
              t.sent();
              t.label = 2;
            case 2:
              o(true);
              return [2];
          }
        });
      });
    });
  };
  _ctor.prototype._getRomete = function (e, t) {
    var o = this;
    undefined === t && (t = 3);
    var n = this._secret_key;
    var i = function () {
      return new Promise(function (o) {
        t--;
        o($9YPNetStorage.default.getStorage(n, e) || null);
      });
    };
    return new Promise(function (e) {
      return cc__awaiter(o, undefined, undefined, function () {
        var o;
        var n;
        return cc__generator(this, function (a) {
          switch (a.label) {
            case 0:
              if (t >= 0) {
                return [4, i()];
              } else {
                return [3, 5];
              }
            case 1:
              if (o = a.sent()) {
                return e(o), [3, 4];
              } else {
                return [3, 2];
              }
            case 2:
              n = e;
              return [4, i()];
            case 3:
              n.apply(undefined, [a.sent()]);
              a.label = 4;
            case 4:
              return [3, 6];
            case 5:
              e(null);
              a.label = 6;
            case 6:
              return [2];
          }
        });
      });
    });
  };
  _ctor.prototype._setRomete = function (e, t, o) {
    var n = this;
    undefined === o && (o = 3);
    var i = this._secret_key;
    var c = function () {
      return new Promise(function (c) {
        return cc__awaiter(n, undefined, undefined, function () {
          var n;
          return cc__generator(this, function (a) {
            switch (a.label) {
              case 0:
                o--;
                return [4, $9YPNetStorage.default.setStorage(i, e, t)];
              case 1:
                n = a.sent();
                c(n);
                return [2];
            }
          });
        });
      });
    };
    return new Promise(function (e) {
      return cc__awaiter(n, undefined, undefined, function () {
        var t;
        return cc__generator(this, function (n) {
          switch (n.label) {
            case 0:
              if (o >= 0) {
                return [4, c()];
              } else {
                return [3, 5];
              }
            case 1:
              if (n.sent()) {
                return e(true), [3, 4];
              } else {
                return [3, 2];
              }
            case 2:
              t = e;
              return [4, c()];
            case 3:
              t.apply(undefined, [n.sent()]);
              n.label = 4;
            case 4:
              return [3, 6];
            case 5:
              $9LogManager.LogMgr.error("上传云存档失败");
              e(false);
              n.label = 6;
            case 6:
              return [2];
          }
        });
      });
    });
  };
  _ctor.prototype._hasRomete = function () {
    try {
      // 安全检查所有依赖模块
      const hasGameConfig = $9GameConfig && $9GameConfig.default && $9GameConfig.default.appConfig && $9GameConfig.default.appConfig.Publish;
      const hasUpload = this._isUpload;
      const hasPlatformManager = $9PlatformManager && $9PlatformManager.PlatformMgr;

      if (!hasGameConfig || !hasUpload || !hasPlatformManager) {
        return false;
      }

      const isWxGame = $9PlatformManager.PlatformMgr.isWxGame && $9PlatformManager.PlatformMgr.isWxGame();
      const isDyGame = $9PlatformManager.PlatformMgr.isDyGame && $9PlatformManager.PlatformMgr.isDyGame();
      const isKsGame = $9PlatformManager.PlatformMgr.isKsGame && $9PlatformManager.PlatformMgr.isKsGame();

      return isWxGame || isDyGame || isKsGame;
    } catch (error) {
      console.error("DataManager: _hasRomete方法出错", error);
      return false;
    }
  };
  _ctor.prototype._isEmpyt = function (e) {
    return null == e || null == e || "" == e;
  };
  return _ctor;
}($1$9SingletonManager.SingletonMgr);
exports.default = def_DataManager;