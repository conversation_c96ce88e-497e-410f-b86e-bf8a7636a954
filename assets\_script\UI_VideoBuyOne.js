/**
 * 视频购买弹窗UI组件
 *
 * 功能说明：
 * 1. 显示视频广告奖励弹窗界面
 * 2. 处理用户点击观看广告或关闭的操作
 * 3. 支持显示金币奖励数量和相关信息
 * 4. 管理广告播放成功/失败的回调处理
 *
 * 使用场景：
 * - 玩家可以通过观看视频广告获得游戏内奖励
 * - 支持自定义标题、描述信息和奖励数量
 * - 提供成功和失败的回调机制
 *
 * UI结构：
 * - 标题文本：显示弹窗标题
 * - 信息文本：显示详细说明
 * - 奖励显示：显示可获得的金币数量
 * - 操作按钮：观看广告按钮和关闭按钮
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_decorate = __decorate;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

// 依赖模块导入
const VideoBuyOneUIBindings = require("UI_VideoBuyOne_UIBindings");
const ADManager = require("ADManager");
const UIView = require("UIView");
const ConstCommon = require("Const_Common");

// Cocos Creator 装饰器
const { ccclass, property, inspector } = cc._decorator;

/**
 * 视频购买弹窗数据接口
 * 定义传入组件的数据结构
 *
 * @interface VideoBuyOneData
 */
const VideoBuyOneDataInterface = {
  /** 弹窗标题文本 */
  titleStr: "",

  /** 弹窗信息描述文本 */
  infoStr: "",

  /** 奖励金币数量（0表示无金币奖励） */
  coinCount: 0,

  /** 点击观看广告成功后的回调函数 */
  clickCallBack: null,

  /** 点击关闭或广告失败后的回调函数 */
  failCallBack: null
};

/**
 * 视频购买弹窗UI组件类
 *
 * 继承自UIView，提供视频广告奖励弹窗的完整功能
 */
const VideoBuyOneUIComponent = function (BaseUIView) {

  /**
   * 构造函数
   * 初始化视频购买弹窗组件
   */
  function VideoBuyOneUI() {
    const instance = BaseUIView !== null && BaseUIView.apply(this, arguments) || this;

    /** UI绑定组件，包含所有UI元素的引用 */
    instance.auto_ui = null;

    /** 打开弹窗时传入的数据 */
    instance.openData = null;

    return instance;
  }

  // 设置继承关系
  cc_extends(VideoBuyOneUI, BaseUIView);

  /**
   * 显示弹窗时的初始化方法
   *
   * @param {VideoBuyOneDataInterface} openData - 弹窗显示数据
   *
   * 执行流程：
   * 1. 保存传入的数据
   * 2. 设置标题和信息文本
   * 3. 根据金币数量显示相应的UI状态
   * 4. 更新金币数量显示
   */
  VideoBuyOneUI.prototype._show = function (openData) {
    // 参数验证
    if (!openData) {
      console.error("VideoBuyOneUI: openData 参数不能为空");
      return;
    }

    // 保存打开数据
    this.openData = openData;

    // 获取UI绑定组件
    const uiBindings = this.auto_ui;
    if (!uiBindings) {
      console.error("VideoBuyOneUI: auto_ui 未正确初始化");
      return;
    }

    // 设置标题和信息文本
    uiBindings.txt_titleLabel.string = openData.titleStr || "视频奖励";
    uiBindings.txt_infoLabel.string = openData.infoStr || "观看视频获得奖励";

    // 根据金币数量控制UI显示状态
    const hasCoins = openData.coinCount > 0;

    // 显示/隐藏金币相关UI元素
    if (uiBindings.node_type && uiBindings.node_type.children.length >= 3) {
      uiBindings.node_type.children[1].active = hasCoins;  // 金币图标
      uiBindings.node_type.children[2].active = !hasCoins; // 无金币状态
    }

    // 设置金币数量文本
    if (hasCoins && uiBindings.txt_countLabel) {
      uiBindings.txt_countLabel.string = String(openData.coinCount);
    }
  };

  /**
   * 处理UI点击事件
   *
   * @param {cc.Node} clickTarget - 被点击的UI节点
   *
   * 支持的点击事件：
   * - 关闭按钮：关闭弹窗并触发失败回调
   * - 观看广告按钮：播放视频广告并处理结果
   */
  VideoBuyOneUI.prototype._clickListener = function (clickTarget) {
    const self = this;

    // 获取UI绑定组件
    const uiBindings = this.auto_ui;
    if (!uiBindings) {
      console.error("VideoBuyOneUI: auto_ui 未正确初始化");
      return;
    }

    // 根据点击目标执行相应操作
    switch (clickTarget) {
      case uiBindings.btn_closeBtn:
        // 处理关闭按钮点击
        this._handleCloseButtonClick();
        break;

      case uiBindings.btn_clickBtn:
        // 处理观看广告按钮点击
        this._handleWatchAdButtonClick();
        break;

      default:
        console.warn("VideoBuyOneUI: 未知的点击目标", clickTarget);
        break;
    }
  };

  /**
   * 处理关闭按钮点击事件
   * @private
   */
  VideoBuyOneUI.prototype._handleCloseButtonClick = function () {
    // 触发失败回调
    if (this.openData && this.openData.failCallBack) {
      try {
        this.openData.failCallBack();
      } catch (error) {
        console.error("VideoBuyOneUI: 执行失败回调时出错", error);
      }
    }

    // 关闭弹窗
    this._close();
  };

  /**
   * 处理观看广告按钮点击事件
   * @private
   */
  VideoBuyOneUI.prototype._handleWatchAdButtonClick = function () {
    const self = this;

    if (!this.openData) {
      console.error("VideoBuyOneUI: openData 为空，无法播放广告");
      return;
    }

    // 获取广告标题（用于统计）
    const adTitle = this.openData.titleStr || "视频奖励广告";

    // 播放激励视频广告
    ADManager.ADMgr.showRewardedVideo(
      adTitle,
      function onAdSuccess() {
        // 广告播放成功回调
        if (self.openData && self.openData.clickCallBack) {
          try {
            self.openData.clickCallBack();
          } catch (error) {
            console.error("VideoBuyOneUI: 执行成功回调时出错", error);
          }
        }

        // 关闭弹窗
        self._close();
      },
      function onAdFailed() {
        // 广告播放失败回调（可选）
        console.warn("VideoBuyOneUI: 视频广告播放失败");

        // 可以在这里添加失败处理逻辑
        if (self.openData && self.openData.failCallBack) {
          try {
            self.openData.failCallBack();
          } catch (error) {
            console.error("VideoBuyOneUI: 执行失败回调时出错", error);
          }
        }
      },
      1,    // 广告数量
      true, // 是否强制显示
      ConstCommon.ShowAdType.关卡内广告 // 广告类型
    );
  };

  // 应用Cocos Creator装饰器
  cc_decorate([property(VideoBuyOneUIBindings.default)], VideoBuyOneUI.prototype, "auto_ui", undefined);

  return cc_decorate([
    ccclass,
    inspector("packages://base-ui/dist/inspector.js")
  ], VideoBuyOneUI);

}(UIView.default);

// 导出视频购买弹窗UI组件
exports.default = VideoBuyOneUIComponent;