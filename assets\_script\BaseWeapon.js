/**
 * 武器基类
 *
 * 功能说明：
 * 1. 定义所有武器的基础属性和行为
 * 2. 管理武器的生命周期（初始化、攻击、受伤、死亡）
 * 3. 处理武器的技能系统和Buff效果
 * 4. 管理武器的动画和视觉效果
 * 5. 处理武器与敌人的交互逻辑
 *
 * 核心系统：
 * - 属性系统：攻击力、血量、暴击等基础属性
 * - 技能系统：武器技能的管理和释放
 * - Buff系统：增益效果的应用和管理
 * - 动画系统：武器的移动和攻击动画
 * - 碰撞系统：与敌人的碰撞检测
 *
 * 设计模式：
 * - 组件模式：作为Cocos Creator组件挂载到节点
 * - 策略模式：不同武器类型的差异化行为
 * - 观察者模式：事件驱动的状态变化
 *
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_decorate = __decorate;
const cc_awaiter = __awaiter;
const cc_generator = __generator;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

// 依赖模块导入
const EventManager = require("EventManager");
const NodePoolManager = require("NodePoolManager");
const UITools = require("UITools");
const CommonUtils = require("CommonUtils");
const BundleType = require("BundleType");
const EventTypes = require("EvenType");
const ExcelData = require("Excel");
const ObjectBase = require("Object");
const ConstGame = require("Const_Game");
const RenderGroup = require("Render_Group");
const BulletFactory = require("BulletFactory");
const TraileOrPraticeFactory = require("TraileOrPraticeFactory");
const WeaponFactory = require("WeaponFactory");
const ItemHurt = require("Item_Hurt");
const ItemKuangre = require("Item_kuangre");
const ItemMosterHp = require("Item_mosterhp");
const ItemTraile = require("Item_traile");
const LogicBuffControl = require("Logic_BuffControl");
const LogicGame = require("Logic_Game");
const LogicGameDataSearch = require("Logic_GameDataSearch");
const LogicGameEffControl = require("Logic_GameEffControl");
const LogicGameMaths = require("Logic_GameMaths");
const BaseBullet = require("BaseBullet");
const BaseMoster = require("BaseMoster");
const BaseWeaponSkill = require("BaseWeaponSkill");
const BaseWharf = require("BaseWharf");

// Cocos Creator 装饰器
const { ccclass, property, inspector } = cc._decorator;
/**
 * 武器基类组件
 *
 * 继承自Object基类，提供完整的武器功能实现
 */
const BaseWeaponClass = function (BaseObjectClass) {

  /**
   * 构造函数
   * 初始化武器的所有基础属性
   */
  function BaseWeapon() {
    const instance = BaseObjectClass !== null && BaseObjectClass.apply(this, arguments) || this;

    // ==================== 动画相关属性 ====================
    /** 动画节点数组 - 用于武器的浮动动画效果 */
    instance.aniNode = [];

    /** 船只精灵数组 - 存储所有需要材质处理的精灵组件 */
    instance.shipSpriteArray = [];

    /** 船只骨骼动画数组 - 存储所有需要材质处理的骨骼动画组件 */
    instance.shipSpineArray = [];

    /** 发射位置节点 - 子弹发射的起始位置 */
    instance.node_sendpos = null;

    /** 移动浪花节点 - 武器移动时的视觉效果 */
    instance.moveLang = null;

    /** 停止浪花节点 - 武器停止时的视觉效果 */
    instance.stopLang = null;

    // ==================== 数据相关属性 ====================
    /** 武器自定义属性数据 */
    instance.myattrs = null;

    /** 动画节点初始位置记录 */
    instance.aniNodeStartPos = {};

    /** 武器配置数据（私有） */
    instance._weapondata = null;

    /** 武器属性数据（私有） */
    instance._attrs = null;

    // ==================== Buff和技能系统 ====================
    /** 当前生效的Buff列表 */
    instance.buffs = [];

    /** 符咒效果列表 */
    instance.fuzus = [];

    // ==================== 战斗相关属性 ====================
    /** 攻击间隔时间计数器 */
    instance.attackTime = 0;

    /** 更新时间间隔 */
    instance.updateTime = 0.1;

    /** 是否正在运行状态 */
    instance.isrun = false;

    /** 是否处于延迟状态 */
    instance.isdealy = false;

    /** 血量显示节点 */
    instance.hpNode = null;

    /** 已损失的血量 */
    instance.changhp = 0;

    // ==================== 移动和动画控制 ====================
    /** 船只是否处于移动状态 */
    instance.shipIsMove = false;

    /** 是否正在执行攻击缓动动画 */
    instance.isAtkTweenTime = false;

    /** 记录时间（用于各种计时功能） */
    instance.jilutime = 0;

    /** 通用时间变量 */
    instance.t = 0;

    /** 插值速度 */
    instance.lerpSpeed = 0.1;

    /** 当前角度 */
    instance.nowangle = 0;

    /** 缓动对象引用 */
    instance.tw = null;

    // ==================== 旋转相关常量 ====================
    /** 旋转速度常量 */
    instance.ROTATION_SPEED = 150;

    /** 当前角度状态 */
    instance.currentAngle = 0;

    // ==================== 其他属性 ====================
    /** 偏移等级 */
    instance.offRank = 1;

    /** 拉长偏移值 */
    instance.lachangoff = 5;

    return instance;
  }

  // 设置继承关系
  cc_extends(BaseWeapon, BaseObjectClass);
  // ==================== 属性访问器 ====================

  /**
   * 武器属性数据访问器
   * 包含攻击力、血量、暴击等战斗属性
   */
  Object.defineProperty(BaseWeapon.prototype, "attrs", {
    get: function () {
      return this._attrs;
    },
    set: function (newAttrs) {
      this._attrs = newAttrs;
    },
    enumerable: false,
    configurable: true
  });

  /**
   * 武器配置数据访问器
   * 包含从Excel配置表读取的武器基础数据
   */
  Object.defineProperty(BaseWeapon.prototype, "weapondata", {
    get: function () {
      return this._weapondata;
    },
    set: function (newWeaponData) {
      this._weapondata = newWeaponData;
    },
    enumerable: false,
    configurable: true
  });

  // ==================== Buff系统方法 ====================

  /**
   * 添加Buff效果到武器
   *
   * @param {Object} buff - Buff效果对象
   */
  BaseWeapon.prototype.setBuff = function (buff) {
    if (!buff) {
      console.warn("BaseWeapon: 尝试添加空的Buff效果");
      return;
    }

    this.buffs.push(buff);
    console.log(`BaseWeapon: 添加Buff效果`, buff);
  };

  /**
   * 设置偏移等级
   * 预留方法，用于特殊武器的偏移处理
   */
  BaseWeapon.prototype.setOffRank = function () {
    // 预留方法，子类可以重写实现特定逻辑
  };

  /**
   * 获取当前所有生效的Buff
   *
   * @returns {Array} 包含符咒Buff和普通Buff的完整列表
   */
  BaseWeapon.prototype.getNewBuff = function () {
    let allBuffs = [];

    // 收集符咒产生的Buff效果
    this.fuzus.forEach(function (fuzu) {
      if (fuzu.fuzubuffs && Array.isArray(fuzu.fuzubuffs)) {
        allBuffs = allBuffs.concat(fuzu.fuzubuffs);
      }
    });

    // 合并普通Buff效果
    allBuffs = allBuffs.concat(this.buffs);

    return allBuffs;
  };
  _ctor.prototype.setFuZu = function (e) {
    var t = this.isFuZu(e.fuzutype);
    var o = this.fuzus[t];
    o && e.fuzhulevel > o.fuzhulevel && (this.fuzus[t] = e);
    if (!o) {
      var n = this.node.getChildByName("krpoint");
      if (n) {
        var i = $9NodePoolManager.NodePoolMgr.get("item_kuangre");
        $9Logic_Game.default.renderNode.addOhterEff(i, 1, "kuangre");
        i.getComponent($9Item_kuangre.default).init(n);
        e.fuzueffNode = i;
      }
      this.fuzus.push(e);
    }
  };
  _ctor.prototype.cleanAllFuZu = function () {
    this.fuzus.forEach(function (e) {
      e.fuzueffNode && $9NodePoolManager.NodePoolMgr.put("item_kuangre", e.fuzueffNode);
    });
    this.fuzus = [];
  };
  _ctor.prototype.cleanFuZu = function (e) {
    var t = this.isFuZu(e.fuzutype);
    this.fuzus.splice(t, 1);
    e.fuzueffNode && $9NodePoolManager.NodePoolMgr.put("item_kuangre", e.fuzueffNode);
  };
  _ctor.prototype.isFuZu = function (e) {
    return this.fuzus.findIndex(function (t) {
      return t.fuzutype == e;
    });
  };
  _ctor.prototype.onLoad = function () {
    var e = this;
    this.shipSpriteArray.forEach(function (t) {
      return cc__awaiter(e, undefined, undefined, function () {
        var e;
        return cc__generator(this, function (o) {
          switch (o.label) {
            case 0:
              return [4, this._loadRes("game", "shader/flash_red", cc.Material)];
            case 1:
              e = o.sent();
              t.setMaterial(0, e);
              return [2];
          }
        });
      });
    });
    this.shipSpineArray.forEach(function (t) {
      return cc__awaiter(e, undefined, undefined, function () {
        var e;
        return cc__generator(this, function (o) {
          switch (o.label) {
            case 0:
              if ("$spine_effectDiCeng" == t.node.name || "$spine_effectShangCeng" == t.node.name) {
                return [3, 2];
              } else {
                return [4, this._loadRes("game", "shader/flash_red", cc.Material)];
              }
            case 1:
              e = o.sent();
              t.setMaterial(0, e);
              o.label = 2;
            case 2:
              return [2];
          }
        });
      });
    });
    this.moveLang && this.moveLang.children.forEach(function (e) {
      e.children.forEach(function (e) {
        var t = e.getComponent(sp.Skeleton);
        t && (t.timeScale = 1.5);
      });
    });
  };
  _ctor.prototype.onEnable = function () {
    $9EventManager.EventMgr.addEventListener($9EvenType.EVENT_TYPE.Game_Rock_TouchStart, this.setMoveStatus, this);
    $9EventManager.EventMgr.addEventListener($9EvenType.EVENT_TYPE.Game_Rock_TouchEnd, this.setStopStatus, this);
    if (this.moveLang) {
      this.moveLang.active = true;
      this.moveLang.opacity = 0;
    }
    if (this.stopLang) {
      this.stopLang.active = true;
      this.stopLang.opacity = 0;
    }
    this.ChangeIsMoveTime({
      isMove: false,
      isStart: true
    });
  };
  _ctor.prototype.onDisable = function () {
    $9EventManager.EventMgr.rmEventListener($9EvenType.EVENT_TYPE.Game_Rock_TouchStart, this.setMoveStatus, this);
    $9EventManager.EventMgr.rmEventListener($9EvenType.EVENT_TYPE.Game_Rock_TouchEnd, this.setStopStatus, this);
  };
  _ctor.prototype.setMoveStatus = function () {
    this.ChangeIsMoveTime({
      isMove: true,
      isStart: false
    });
  };
  _ctor.prototype.setStopStatus = function () {
    this.ChangeIsMoveTime({
      isMove: false,
      isStart: false
    });
  };
  _ctor.prototype.ChangeIsMoveTime = function (e) {
    if (this.shipIsMove != e.isMove || e.isStart) {
      this.shipIsMove = e.isMove;
      if (this.moveLang) {
        this.moveLang.stopAllActions(), e.isMove ? this.moveLang.runAction(cc.fadeIn(1)) : this.moveLang.runAction(cc.fadeOut(1));
      }
      if (this.stopLang) {
        this.stopLang.stopAllActions(), e.isMove ? this.stopLang.runAction(cc.fadeOut(1)) : this.stopLang.runAction(cc.fadeIn(1));
      }
    }
  };
  _ctor.prototype.isDie = function () {
    return this._status == $9Const_Game.BuildStatus.die;
  };
  _ctor.prototype.flyHurtNum = function (e, t) {
    var o = $9NodePoolManager.NodePoolMgr.get("Item_Hurt");
    $9Logic_Game.default.renderNode.addHurtForIndex(o);
    o.getComponent($9Item_Hurt.default).init(this.node, e, t, $9Const_Game.HurtTag.怪物伤害);
  };
  _ctor.prototype.hurt = function (e) {
    if (this._status != $9Const_Game.BuildStatus.die) {
      e = Math.max(e, 1);
      console.log("自身被伤害", e);
      var t = this.getShield();
      if (t >= e) {
        this.setShield(t - e);
        return void this.addWeaponHp(true);
      }
      this.changhp += e - t;
      var o = this.getHp() - this.changhp;
      this.addWeaponHp();
      if (o <= 0) {
        o = 0;
        this._status = $9Const_Game.BuildStatus.die;
        $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.Game_Weapon_Die, this);
      }
      $9EventManager.EventMgr.dispatchEvent($9EvenType.EVENT_TYPE.Game_Weapon_Hp, this);
      this._flashWhite();
    }
  };
  _ctor.prototype.addChangHp = function (e) {
    this.changhp -= e;
    this.changhp < 0 && (this.changhp = 0);
    var t = $9Logic_GameDataSearch.default.getEffData(16);
    var o = {
      id: t.id,
      name: t.pfbname,
      animation: t.aniname,
      pos: this.node.getPosition(),
      effecttype: $9Render_Group.EffectAdditionType.普通攻击,
      effectharm: 0,
      targets: [this],
      duration: 2
    };
    $9Logic_GameEffControl.default.playEffect(o);
  };
  _ctor.prototype.hurtPro = function () {
    var e = this.getHp();
    return this.changhp / e;
  };
  _ctor.prototype._flashWhite = function () {
    var e = this;
    var t = function (e) {
      if (!e._isFlash) {
        e._isFlash = true;
        var t = e.color;
        t.r;
        var o = t.g;
        var n = t.b;
        var i = t.a;
        cc.tween({
          rate: .3
        }).to(.2, {
          rate: .1
        }, {
          onUpdate: function (t) {
            var a = t.rate;
            e.color = cc.color(255 * a, o, n, i);
          }
        }).call(function () {
          e.color = cc.color(255, o, n, i);
          e._isFlash = false;
        }).start();
      }
    };
    this.shipSpriteArray.forEach(function (o) {
      return cc__awaiter(e, undefined, undefined, function () {
        return cc__generator(this, function () {
          t(o.node);
          return [2];
        });
      });
    });
    this.shipSpineArray.forEach(function (o) {
      return cc__awaiter(e, undefined, undefined, function () {
        return cc__generator(this, function () {
          t(o.node);
          return [2];
        });
      });
    });
  };
  _ctor.prototype.init = function (e) {
    var t = this;
    this.reset();
    this.formatAttrs(e);
    this._status = $9Const_Game.BuildStatus.stand;
    this.scheduleOnce(function () {
      t.playerAni();
    });
    this.addLang();
    this.updateNowBuffs();
    this.isrun = true;
    this.ResetChildAngle();
  };
  _ctor.prototype.updateNowBuffs = function () {
    this.buffs = [];
    var e = $9Logic_BuffControl.default.getWordsBuffs(this.weapondata.id, this);
    var t = $9Logic_BuffControl.default.getWordsLevelBuffs(this.weapondata.id, this.myattrs.dressUpEquip.equipLevel);
    var o = $9Logic_BuffControl.default.getWeaponResidentFuZu(this);
    var n = $9Logic_BuffControl.default.getOtherWeaponBuff(this.weapondata.id);
    this.buffs = this.buffs.concat(o, e, t, n);
    $9Logic_Game.default.showLog("当前buff数据", this.buffs, this.weapondata.id);
  };
  _ctor.prototype.addWeaponHp = function (e) {
    var t = this;
    undefined === e && (e = false);
    if (this.hpNode && !this.hpNode.getComponent($9Item_mosterhp.default).isput) {
      this.hpNode.getComponent($9Item_mosterhp.default).cleanHp();
      this.hpNode = null;
    }
    this.scheduleOnce(function () {
      t.hpNode = $9NodePoolManager.NodePoolMgr.get("item_mosterhp");
      t.hpNode.getComponent($9Item_mosterhp.default).init(t, true);
      $9Logic_Game.default.renderNode.addMosterHp(t.hpNode, 1);
    }, .05);
  };
  _ctor.prototype.addLang = function () {
    return cc__awaiter(this, undefined, undefined, function () {
      var e;
      var t;
      return cc__generator(this, function (o) {
        switch (o.label) {
          case 0:
            if (e = this.node.getChildByName("tuoweiPoint")) {
              return [4, $9TraileOrPraticeFactory.default.getCustomPratice("item_chuanlang")];
            } else {
              return [3, 2];
            }
          case 1:
            t = o.sent();
            $9Logic_Game.default.renderNode.addTrails(t, 0);
            e.y = -(this.node.height - 15);
            13 == this.weapondata.id && (e.x = this.node.width / 4);
            102 != this.weapondata.id && 4 != this.weapondata.id || (e.x = -this.node.width / 4);
            t.getComponent($9Item_traile.default).init(this, 10);
            o.label = 2;
          case 2:
            return [2];
        }
      });
    });
  };
  _ctor.prototype.initBox = function () {
    this.trigger = true;
    this.size.x = .8 * this.node.width;
    this.size.y = .8 * this.node.height;
    this.center.y = -this.size.y / 2;
    0 != this.center.x && (this.center.x = -this.size.x / 2);
    this.node.group = $9Const_Game.GameNodeGroup.player;
    e.prototype.onLoad.call(this);
    var t = this.node.getChildByName("krpoint");
    if (t) {
      t.y = -this.node.height / 2;
      13 == this.weapondata.id && (t.x = this.node.width / 4);
      102 != this.weapondata.id && 4 != this.weapondata.id || (t.x = -this.node.width / 4);
    }
  };
  _ctor.prototype.playerAni = function (e) {
    var t = this;
    undefined === e && (e = 1);
    this.stopAni();
    !function o() {
      var n = $9CommonUtils.default.limitInteger(4, 10);
      var i = $9CommonUtils.default.limitInteger(4, 10);
      Math.random() >= .5 && (n = -n);
      Math.random() >= .5 && (i = -i);
      t.aniNode.forEach(function (o) {
        t.aniNodeStartPos[o.uuid] || (t.aniNodeStartPos[o.uuid] = o.getPosition());
        var a = t.aniNodeStartPos[o.uuid];
        cc.tween(o).to(1 * e, {
          x: n + a.x,
          y: i + a.y
        }).to(1 * e, {
          x: a.x,
          y: a.y
        }).call(function () {}).start();
      });
      t.scheduleOnce(function () {
        o();
      }, 2 * e);
    }();
  };
  _ctor.prototype.stopAni = function () {
    this.aniNode.forEach(function (e) {
      cc.Tween.stopAllByTarget(e);
    });
  };
  _ctor.prototype.getWeaponBullet = function (e, t) {
    return cc__awaiter(this, undefined, undefined, function () {
      var o;
      var n;
      return cc__generator(this, function (i) {
        switch (i.label) {
          case 0:
            if (e) {
              return o = null, e.getComponent($9BaseMoster.default) && (o = e.getComponent($9BaseMoster.default)), e.getComponent($9BaseWharf.default) && (o = e.getComponent($9BaseWharf.default)), !o || o.isDie() ? [3, 2] : [4, $9BulletFactory.default.getBullet(t)];
            } else {
              return [3, 4];
            }
          case 1:
            if (n = i.sent()) {
              return $9Logic_Game.default.renderNode.addBulletForIndex(n, t, this.getBulletRenderNode(t)), [2, n];
            } else {
              return [2, null];
            }
          case 2:
            return [2, null];
          case 3:
            return [3, 5];
          case 4:
            return [2, null];
          case 5:
            return [2];
        }
      });
    });
  };
  _ctor.prototype.formatAttrs = function (e) {
    var t = this;
    this.weapondata = $9Excel.Excel.haizhanwuqi(e.id);
    this.myattrs = e;
    var o = {
      weaponlevel: e.dressUpEquip.equipLevel,
      attackhp: this.weapondata.attackHp,
      attack: this.weapondata.attack,
      attackskill: [],
      critical: this.weapondata.baojilv / 100,
      critical_dmg: this.weapondata.baojivalue / 100,
      dmg_increased: 0,
      must_critical: false
    };
    this._attrs = o;
    this.weapondata.attackskill.forEach(function (e) {
      t.addWeaponSkill(e);
    });
  };
  _ctor.prototype.getHpPro = function () {
    return (this.getHp() - this.changhp) / this.getHp();
  };
  _ctor.prototype.addWeaponSkill = function (e) {
    var t = $9Excel.Excel.haizhanweaponskill(e);
    if (t && 5 != t.attacktype && !this.attrs.attackskill.find(function (e) {
      return e.isSkill(t);
    })) {
      var o = new $9BaseWeaponSkill.default();
      o.init(t, this);
      this.attrs.attackskill.push(o);
    }
  };
  _ctor.prototype.getSkilld = function (e) {
    var t = $9Excel.Excel.haizhanweaponskill(e);
    if (t) {
      return this.attrs.attackskill.find(function (e) {
        return e.isSkill(t);
      });
    } else {
      return null;
    }
  };
  _ctor.prototype.getBulletSkilld = function (e) {
    return this.attrs.attackskill.find(function (t) {
      return t.isBullet(e);
    });
  };
  _ctor.prototype.getAttackScope = function (e) {
    var t = this.attrs.attackskill.find(function (t) {
      return t.isBullet(e);
    });
    if (t) {
      return t.getAttackScope();
    } else {
      return 300;
    }
  };
  _ctor.prototype.getAttackDamage = function (e) {
    var t = this.getAttack();
    var o = $9Logic_BuffControl.default.getWeaponCritical(1, this.getNewBuff());
    var n = $9Logic_BuffControl.default.getWeaponCritical(2, this.getNewBuff());
    var i = $9Const_Game.HurtType.normal;
    var a = this.attrs.attackskill.find(function (t) {
      return t.isBullet(e);
    });
    a && $9Logic_BuffControl.default.isBuffCertainlyCritical(this.getNewBuff(), a) && (i = $9Const_Game.HurtType.critical);
    if (i == $9Const_Game.HurtType.critical || Math.random() <= o) {
      i = $9Const_Game.HurtType.critical;
      t *= 1 + this.attrs.critical_dmg + n;
    }
    return {
      damagetype: i,
      attack: t = $9Logic_BuffControl.default.getWeaponDamage(this.getNewBuff(), t, a)
    };
  };
  _ctor.prototype.getHp = function () {
    return $9Logic_BuffControl.default.getWeaponHp(this.weapondata.id, this.getNewBuff(), this._attrs.attackhp, this.myattrs.dressUpEquip.equipStar);
  };
  _ctor.prototype.GetAddOtherShield = function () {
    var e = 0;
    var t = $9Excel.Excel.haizhanweaponskill(29);
    t && (e = t.ohtervalue[0]);
    return $9Logic_BuffControl.default.getWeaponAddShield(this.weapondata.id, this.getNewBuff(), e, this.getHp());
  };
  _ctor.prototype.getShield = function () {
    return $9Logic_BuffControl.default.GetShieldValue(this.node.uuid);
  };
  _ctor.prototype.setShield = function (e) {
    $9Logic_BuffControl.default.SetShieldValue(this.node.uuid, e);
  };
  _ctor.prototype.getAttack = function () {
    return $9Logic_BuffControl.default.getWeaponAttack(this.weapondata.id, this.getNewBuff(), this._attrs.attack, this.myattrs.dressUpEquip.equipStar);
  };
  _ctor.prototype.findNowTagret = function (e, t, o) {
    var n = this.getBulletSkilld(e);
    var i = $9Logic_GameDataSearch.default.getBulletData(e);
    var a = this.node.getPosition();
    o && (a = o);
    var r = $9Logic_Game.default.getRecentWharf(a, 1, n.getAttackScope() - 50, t);
    r || (r = $9Logic_Game.default.getRecentMoster(a, i.attacktype, n.getAttackScope() - 50, t));
    return r;
  };
  _ctor.prototype.getBulletRenderNode = function (e) {
    var t = $9Logic_GameDataSearch.default.getBulletData(e);
    if (t) {
      return t.bulletrenderNode;
    }
  };
  _ctor.prototype.attackBack = function () {};
  _ctor.prototype.createBullet = function (e, t, o) {
    return cc__awaiter(this, undefined, undefined, function () {
      var n;
      var i;
      var a;
      return cc__generator(this, function (r) {
        switch (r.label) {
          case 0:
            o || (o = this.node_sendpos ? this.node_sendpos : this.node);
            return [4, this.getWeaponBullet(t, e)];
          case 1:
            if (n = r.sent()) {
              i = n.getComponent($9BaseBullet.default);
              a = {
                id: e,
                createNode: this,
                from: o,
                target: t,
                callback: this.attackBack.bind(this)
              };
              i.init(a);
              this.createBulletEnd(t, n);
              t = null;
            }
            return [2];
        }
      });
    });
  };
  _ctor.prototype.createBulletEnd = function () {};
  _ctor.prototype.updateState = function () {
    switch (this._status) {
      case $9Const_Game.BuildStatus.die:
        var e = this.weapondata.weapondieeff;
        $9Logic_GameEffControl.default.playerDieEff(e, this.node.getPosition(), null);
        this.cleanWeapon();
    }
  };
  _ctor.prototype.cleanWeapon = function () {
    this.isrun = false;
    this.currentAngle = 0;
    this.remove(false);
    $9WeaponFactory.default.putNode(this.node, true);
    $9Logic_Game.default.gamePlayer.cleanBiaoJi(this.myattrs.dressUpEquip.lockCellIndex);
    this.cleanAllFuZu();
  };
  _ctor.prototype.onUpdate = function (e) {
    if (this.isrun) {
      this.updateState(e);
      this.attrs.attackskill.forEach(function (t) {
        t.onUpdate(e);
      });
      for (var t = 0; t < this.fuzus.length; t++) {
        var o = this.fuzus[t];
        o.fuzutime -= e;
        this.fuzus[t] = o;
        o.fuzutime <= 0 && this.cleanFuZu(o);
      }
    }
  };
  _ctor.prototype.delayLocation = function (e, t, o) {
    if (!this.isdealy || 1 == $9Logic_Game.default.gamePlayer.isOutCode) {
      this.ROTATION_SPEED = 10;
      this.t = 0;
      this.setPosition(cc.v3(e.x, e.y, 0));
      this.setAngle(t);
      return void (this.currentAngle = t);
    }
    if ($9Logic_Game.default.gamerock && $9Logic_Game.default.gamerock.isJoystickActive()) {
      this.ROTATION_SPEED = 2;
      if (this.tw) {
        this.tw.stop();
        this.tw = null;
      }
      var n = (this.node.angle + 90 + 180) * (Math.PI / 180);
      var i = new cc.Vec2(Math.cos(n), Math.sin(n)).multiplyScalar(5);
      this.t += o * this.lerpSpeed;
      this.t > 1 && (this.t = 1);
      var a = cc.misc.lerp(this.node.x, e.x + i.x, this.t);
      var r = cc.misc.lerp(this.node.y, e.y + i.y, this.t);
      this.node.x = a;
      this.node.y = r;
    } else if (!this.tw) {
      this.ROTATION_SPEED = 10;
      this.t = 0;
      this.tw = cc.tween(this.node).to(.4, {
        x: e.x,
        y: e.y
      }).start();
    }
    this.setPosition(cc.v3(this.node.x, this.node.y, 0));
    this.setAngle(t);
  };
  _ctor.prototype.updatePlayerRotation = function (e, t) {
    var o = $9Logic_GameMaths.default.normalizeAngle(e);
    this.currentAngle = $9Logic_GameMaths.default.normalizeAngle(this.currentAngle);
    var n = $9Logic_GameMaths.default.getShortestRotation(this.currentAngle, o);
    this.currentAngle = cc.misc.lerp(this.currentAngle, this.currentAngle + n, t * this.ROTATION_SPEED);
    this.setAngle(this.currentAngle);
  };
  _ctor.prototype.reset = function () {
    this.attackTime = 0;
    this.updateTime = .1;
    this.isrun = false;
    this.isdealy = true;
    this.hpNode = null;
    this.changhp = 0;
    this.shipIsMove = false;
    this.hpNode = null;
  };
  _ctor.prototype.ResetShow = function (e, t) {
    var o = $9Logic_GameDataSearch.default.GetWeaponHeCheng(e);
    if (o && o[t]) {
      if (o[t].weaponspine && o[t].weaponspine.length > 0 && this.shipSpineArray && this.shipSpineArray.length > 0) {
        var n = function (e) {
          var n = i.shipSpineArray[e];
          var a = o[t].weaponspine[e];
          var r = o[t].spineaniname[e];
          n && a && $9UITools.default.loadSpine(n, $9BundleType.BundleNames.effect, "weaponSpine/" + a + "/" + a).then(function (e) {
            e && r && n.setAnimation(0, r, true);
          });
        };
        var i = this;
        for (var a = 0; a < this.shipSpineArray.length; a++) {
          n(a);
        }
      }
      if (o[t].weaponimg && o[t].weaponimg.length > 0 && this.shipSpriteArray && this.shipSpriteArray.length > 0) {
        var r = function (e) {
          var n = c.shipSpriteArray[e];
          var i = o[t].weaponimg[e];
          n && i && c._loadRes($9BundleType.BundleNames.gameyanshi, "weaponImg/" + i, cc.SpriteFrame).then(function (e) {
            if (-1 != i.indexOf("shadow")) {
              n.node.opacity = 100;
              n.node.scale = 1.1;
              n.node.x = n.node.width / 145 * 2;
              n.node.y = n.node.height / 213 * -9;
            }
            n.spriteFrame = e;
          });
        };
        var c = this;
        for (a = 0; a < this.shipSpriteArray.length; a++) {
          r(a);
        }
      }
    }
    this.ResetChildAngle();
  };
  _ctor.prototype.ResetChildAngle = function () {};
  cc__decorate([ccp_property({
    type: cc.Node
  })], _ctor.prototype, "aniNode", undefined);
  cc__decorate([ccp_property([cc.Sprite])], _ctor.prototype, "shipSpriteArray", undefined);
  cc__decorate([ccp_property([sp.Skeleton])], _ctor.prototype, "shipSpineArray", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "node_sendpos", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "moveLang", undefined);
  cc__decorate([ccp_property(cc.Node)], _ctor.prototype, "stopLang", undefined);
  return cc__decorate([ccp_ccclass, ccp_inspector("packages://base-ui/dist/inspector.js")], _ctor);
}($9Object.cObject);
exports.default = def_BaseWeapon;