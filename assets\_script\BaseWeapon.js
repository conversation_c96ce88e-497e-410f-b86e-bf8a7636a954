/**
 * 武器基类（修复版本）
 * 
 * 功能说明：
 * 1. 定义所有武器的基础属性和行为
 * 2. 管理武器的生命周期（初始化、攻击、受伤、死亡）
 * 3. 处理武器的技能系统和Buff效果
 * 4. 管理武器的动画和视觉效果
 * 5. 处理武器与敌人的交互逻辑
 * 
 * 核心系统：
 * - 属性系统：攻击力、血量、暴击等基础属性
 * - 技能系统：武器技能的管理和释放
 * - Buff系统：增益效果的应用和管理
 * - 动画系统：武器的移动和攻击动画
 * - 碰撞系统：与敌人的碰撞检测
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_decorate = __decorate;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

// 依赖模块导入
const $9Object = require("Object");

// Cocos Creator 装饰器
const { ccclass, property, inspector } = cc._decorator;

/**
 * 武器基类组件
 * 
 * 继承自Object基类，提供完整的武器功能实现
 */
const BaseWeaponClass = function (BaseObjectClass) {
  
  /**
   * 构造函数
   * 初始化武器的所有基础属性
   */
  function BaseWeapon() {
    const instance = BaseObjectClass !== null && BaseObjectClass.apply(this, arguments) || this;
    
    // ==================== 动画相关属性 ====================
    /** 动画节点数组 - 用于武器的浮动动画效果 */
    instance.aniNode = [];
    
    /** 船只精灵数组 - 存储所有需要材质处理的精灵组件 */
    instance.shipSpriteArray = [];
    
    /** 船只骨骼动画数组 - 存储所有需要材质处理的骨骼动画组件 */
    instance.shipSpineArray = [];
    
    /** 发射位置节点 - 子弹发射的起始位置 */
    instance.node_sendpos = null;
    
    /** 移动浪花节点 - 武器移动时的视觉效果 */
    instance.moveLang = null;
    
    /** 停止浪花节点 - 武器停止时的视觉效果 */
    instance.stopLang = null;
    
    // ==================== 数据相关属性 ====================
    /** 武器自定义属性数据 */
    instance.myattrs = null;
    
    /** 动画节点初始位置记录 */
    instance.aniNodeStartPos = {};
    
    /** 武器配置数据（私有） */
    instance._weapondata = null;
    
    /** 武器属性数据（私有） */
    instance._attrs = null;
    
    // ==================== Buff和技能系统 ====================
    /** 当前生效的Buff列表 */
    instance.buffs = [];
    
    /** 符咒效果列表 */
    instance.fuzus = [];
    
    // ==================== 战斗相关属性 ====================
    /** 攻击间隔时间计数器 */
    instance.attackTime = 0;
    
    /** 更新时间间隔 */
    instance.updateTime = 0.1;
    
    /** 是否正在运行状态 */
    instance.isrun = false;
    
    /** 是否处于延迟状态 */
    instance.isdealy = false;
    
    /** 血量显示节点 */
    instance.hpNode = null;
    
    /** 已损失的血量 */
    instance.changhp = 0;
    
    // ==================== 移动和动画控制 ====================
    /** 船只是否处于移动状态 */
    instance.shipIsMove = false;
    
    /** 是否正在执行攻击缓动动画 */
    instance.isAtkTweenTime = false;
    
    /** 记录时间（用于各种计时功能） */
    instance.jilutime = 0;
    
    /** 通用时间变量 */
    instance.t = 0;
    
    /** 插值速度 */
    instance.lerpSpeed = 0.1;
    
    /** 当前角度 */
    instance.nowangle = 0;
    
    /** 缓动对象引用 */
    instance.tw = null;
    
    // ==================== 旋转相关常量 ====================
    /** 旋转速度常量 */
    instance.ROTATION_SPEED = 150;
    
    /** 当前角度状态 */
    instance.currentAngle = 0;
    
    // ==================== 其他属性 ====================
    /** 偏移等级 */
    instance.offRank = 1;
    
    /** 拉长偏移值 */
    instance.lachangoff = 5;
    
    return instance;
  }
  
  // 设置继承关系
  cc_extends(BaseWeapon, BaseObjectClass);
  
  // ==================== 属性访问器 ====================
  
  /**
   * 武器属性数据访问器
   * 包含攻击力、血量、暴击等战斗属性
   */
  Object.defineProperty(BaseWeapon.prototype, "attrs", {
    get: function () {
      return this._attrs;
    },
    set: function (newAttrs) {
      this._attrs = newAttrs;
    },
    enumerable: false,
    configurable: true
  });
  
  /**
   * 武器配置数据访问器
   * 包含从Excel配置表读取的武器基础数据
   */
  Object.defineProperty(BaseWeapon.prototype, "weapondata", {
    get: function () {
      return this._weapondata;
    },
    set: function (newWeaponData) {
      this._weapondata = newWeaponData;
    },
    enumerable: false,
    configurable: true
  });
  
  // ==================== Buff系统方法 ====================
  
  /**
   * 添加Buff效果到武器
   * 
   * @param {Object} buff - Buff效果对象
   */
  BaseWeapon.prototype.setBuff = function (buff) {
    if (!buff) {
      console.warn("BaseWeapon: 尝试添加空的Buff效果");
      return;
    }
    
    this.buffs.push(buff);
    console.log("BaseWeapon: 添加Buff效果", buff);
  };
  
  /**
   * 设置偏移等级
   * 预留方法，用于特殊武器的偏移处理
   */
  BaseWeapon.prototype.setOffRank = function () {
    // 预留方法，子类可以重写实现特定逻辑
  };
  
  /**
   * 获取当前所有生效的Buff
   * 
   * @returns {Array} 包含符咒Buff和普通Buff的完整列表
   */
  BaseWeapon.prototype.getNewBuff = function () {
    let allBuffs = [];
    
    // 收集符咒产生的Buff效果
    this.fuzus.forEach(function (fuzu) {
      if (fuzu.fuzubuffs && Array.isArray(fuzu.fuzubuffs)) {
        allBuffs = allBuffs.concat(fuzu.fuzubuffs);
      }
    });
    
    // 合并普通Buff效果
    allBuffs = allBuffs.concat(this.buffs);
    
    return allBuffs;
  };
  
  /**
   * 符咒相关方法（预留接口）
   * 这些方法需要在具体的武器子类中实现
   */
  
  /**
   * 设置符咒效果
   * @param {Object} fuzu - 符咒对象
   */
  BaseWeapon.prototype.setFuZu = function (fuzu) {
    // 预留方法，子类实现
    console.log("BaseWeapon: setFuZu 方法需要在子类中实现");
  };
  
  /**
   * 清理所有符咒效果
   */
  BaseWeapon.prototype.cleanAllFuZu = function () {
    // 预留方法，子类实现
    console.log("BaseWeapon: cleanAllFuZu 方法需要在子类中实现");
  };
  
  /**
   * 武器初始化方法
   * @param {Object} weaponData - 武器数据
   */
  BaseWeapon.prototype.init = function (weaponData) {
    // 预留方法，子类实现
    console.log("BaseWeapon: init 方法需要在子类中实现");
  };
  
  /**
   * 武器更新方法
   * @param {number} deltaTime - 帧间隔时间
   */
  BaseWeapon.prototype.onUpdate = function (deltaTime) {
    // 预留方法，子类实现
    console.log("BaseWeapon: onUpdate 方法需要在子类中实现");
  };

  /**
   * 播放武器浮动动画
   * @param {number} speed - 动画速度倍数，默认为1
   */
  BaseWeapon.prototype.playerAni = function (speed) {
    const self = this;
    if (speed === undefined) {
      speed = 1;
    }

    // 停止之前的动画
    this.stopAni();

    // 开始循环动画
    function startAnimation() {
      // 生成随机偏移量
      const offsetX = Math.floor(Math.random() * 7) + 4; // 4-10的随机数
      const offsetY = Math.floor(Math.random() * 7) + 4; // 4-10的随机数

      // 随机决定方向
      const finalOffsetX = Math.random() >= 0.5 ? offsetX : -offsetX;
      const finalOffsetY = Math.random() >= 0.5 ? offsetY : -offsetY;

      // 如果有动画节点，对第一个子节点执行动画
      if (self.node && self.node.children && self.node.children.length > 0) {
        const targetNode = self.node.children[0];

        cc.tween(targetNode)
          .to(1 * speed, {
            x: finalOffsetX,
            y: finalOffsetY
          })
          .to(1 * speed, {
            x: 0,
            y: 0
          })
          .call(function () {
            // 动画完成后的回调
          })
          .start();
      }

      // 安排下一次动画
      self.scheduleOnce(function () {
        startAnimation();
      }, 2 * speed);
    }

    // 开始动画循环
    startAnimation();
  };

  /**
   * 停止武器浮动动画
   */
  BaseWeapon.prototype.stopAni = function () {
    // 停止所有缓动动画
    if (this.node && this.node.children && this.node.children.length > 0) {
      const targetNode = this.node.children[0];
      cc.Tween.stopAllByTarget(targetNode);
      // 重置位置
      targetNode.setPosition(0, 0);
    }

    // 取消所有定时器
    this.unscheduleAllCallbacks();
  };
  
  // 应用Cocos Creator装饰器
  cc_decorate([property({
    type: cc.Node
  })], BaseWeapon.prototype, "aniNode", undefined);
  
  cc_decorate([property([cc.Sprite])], BaseWeapon.prototype, "shipSpriteArray", undefined);
  
  cc_decorate([property([sp.Skeleton])], BaseWeapon.prototype, "shipSpineArray", undefined);
  
  cc_decorate([property(cc.Node)], BaseWeapon.prototype, "node_sendpos", undefined);
  
  cc_decorate([property(cc.Node)], BaseWeapon.prototype, "moveLang", undefined);
  
  cc_decorate([property(cc.Node)], BaseWeapon.prototype, "stopLang", undefined);
  
  return cc_decorate([
    ccclass, 
    inspector("packages://base-ui/dist/inspector.js")
  ], BaseWeapon);
  
}($9Object.cObject);

// 导出武器基类
exports.default = BaseWeaponClass;
