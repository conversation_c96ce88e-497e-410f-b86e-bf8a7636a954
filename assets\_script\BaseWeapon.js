/**
 * 武器基类（修复版本）
 * 
 * 功能说明：
 * 1. 定义所有武器的基础属性和行为
 * 2. 管理武器的生命周期（初始化、攻击、受伤、死亡）
 * 3. 处理武器的技能系统和Buff效果
 * 4. 管理武器的动画和视觉效果
 * 5. 处理武器与敌人的交互逻辑
 * 
 * 核心系统：
 * - 属性系统：攻击力、血量、暴击等基础属性
 * - 技能系统：武器技能的管理和释放
 * - Buff系统：增益效果的应用和管理
 * - 动画系统：武器的移动和攻击动画
 * - 碰撞系统：与敌人的碰撞检测
 * 
 * <AUTHOR>
 * @version 2.0.0
 */

// TypeScript/JavaScript 工具函数
const cc_extends = __extends;
const cc_decorate = __decorate;

// 模块导出定义
Object.defineProperty(exports, "__esModule", {
  value: true
});

// 依赖模块导入
const $9Object = require("Object");

// Cocos Creator 装饰器
const { ccclass, property, inspector } = cc._decorator;

/**
 * 武器基类组件
 * 
 * 继承自Object基类，提供完整的武器功能实现
 */
const BaseWeaponClass = function (BaseObjectClass) {
  
  /**
   * 构造函数
   * 初始化武器的所有基础属性
   */
  function BaseWeapon() {
    const instance = BaseObjectClass !== null && BaseObjectClass.apply(this, arguments) || this;
    
    // ==================== 动画相关属性 ====================
    /** 动画节点数组 - 用于武器的浮动动画效果 */
    instance.aniNode = [];
    
    /** 船只精灵数组 - 存储所有需要材质处理的精灵组件 */
    instance.shipSpriteArray = [];
    
    /** 船只骨骼动画数组 - 存储所有需要材质处理的骨骼动画组件 */
    instance.shipSpineArray = [];
    
    /** 发射位置节点 - 子弹发射的起始位置 */
    instance.node_sendpos = null;
    
    /** 移动浪花节点 - 武器移动时的视觉效果 */
    instance.moveLang = null;
    
    /** 停止浪花节点 - 武器停止时的视觉效果 */
    instance.stopLang = null;
    
    // ==================== 数据相关属性 ====================
    /** 武器自定义属性数据 */
    instance.myattrs = null;
    
    /** 动画节点初始位置记录 */
    instance.aniNodeStartPos = {};
    
    /** 武器配置数据（私有） */
    instance._weapondata = null;
    
    /** 武器属性数据（私有） */
    instance._attrs = null;
    
    // ==================== Buff和技能系统 ====================
    /** 当前生效的Buff列表 */
    instance.buffs = [];
    
    /** 符咒效果列表 */
    instance.fuzus = [];
    
    // ==================== 战斗相关属性 ====================
    /** 攻击间隔时间计数器 */
    instance.attackTime = 0;
    
    /** 更新时间间隔 */
    instance.updateTime = 0.1;
    
    /** 是否正在运行状态 */
    instance.isrun = false;
    
    /** 是否处于延迟状态 */
    instance.isdealy = false;
    
    /** 血量显示节点 */
    instance.hpNode = null;
    
    /** 已损失的血量 */
    instance.changhp = 0;
    
    // ==================== 移动和动画控制 ====================
    /** 船只是否处于移动状态 */
    instance.shipIsMove = false;
    
    /** 是否正在执行攻击缓动动画 */
    instance.isAtkTweenTime = false;
    
    /** 记录时间（用于各种计时功能） */
    instance.jilutime = 0;
    
    /** 通用时间变量 */
    instance.t = 0;
    
    /** 插值速度 */
    instance.lerpSpeed = 0.1;
    
    /** 当前角度 */
    instance.nowangle = 0;
    
    /** 缓动对象引用 */
    instance.tw = null;
    
    // ==================== 旋转相关常量 ====================
    /** 旋转速度常量 */
    instance.ROTATION_SPEED = 150;
    
    /** 当前角度状态 */
    instance.currentAngle = 0;
    
    // ==================== 其他属性 ====================
    /** 偏移等级 */
    instance.offRank = 1;
    
    /** 拉长偏移值 */
    instance.lachangoff = 5;
    
    return instance;
  }
  
  // 设置继承关系
  cc_extends(BaseWeapon, BaseObjectClass);
  
  // ==================== 属性访问器 ====================
  
  /**
   * 武器属性数据访问器
   * 包含攻击力、血量、暴击等战斗属性
   */
  Object.defineProperty(BaseWeapon.prototype, "attrs", {
    get: function () {
      return this._attrs;
    },
    set: function (newAttrs) {
      this._attrs = newAttrs;
    },
    enumerable: false,
    configurable: true
  });
  
  /**
   * 武器配置数据访问器
   * 包含从Excel配置表读取的武器基础数据
   */
  Object.defineProperty(BaseWeapon.prototype, "weapondata", {
    get: function () {
      return this._weapondata;
    },
    set: function (newWeaponData) {
      this._weapondata = newWeaponData;
    },
    enumerable: false,
    configurable: true
  });
  
  // ==================== Buff系统方法 ====================
  
  /**
   * 添加Buff效果到武器
   * 
   * @param {Object} buff - Buff效果对象
   */
  BaseWeapon.prototype.setBuff = function (buff) {
    if (!buff) {
      console.warn("BaseWeapon: 尝试添加空的Buff效果");
      return;
    }
    
    this.buffs.push(buff);
    console.log("BaseWeapon: 添加Buff效果", buff);
  };
  
  /**
   * 设置偏移等级
   * 预留方法，用于特殊武器的偏移处理
   */
  BaseWeapon.prototype.setOffRank = function () {
    // 预留方法，子类可以重写实现特定逻辑
  };
  
  /**
   * 获取当前所有生效的Buff
   * 
   * @returns {Array} 包含符咒Buff和普通Buff的完整列表
   */
  BaseWeapon.prototype.getNewBuff = function () {
    let allBuffs = [];
    
    // 收集符咒产生的Buff效果
    this.fuzus.forEach(function (fuzu) {
      if (fuzu.fuzubuffs && Array.isArray(fuzu.fuzubuffs)) {
        allBuffs = allBuffs.concat(fuzu.fuzubuffs);
      }
    });
    
    // 合并普通Buff效果
    allBuffs = allBuffs.concat(this.buffs);
    
    return allBuffs;
  };
  
  /**
   * 符咒相关方法（预留接口）
   * 这些方法需要在具体的武器子类中实现
   */
  
  /**
   * 设置符咒效果
   * @param {Object} fuzu - 符咒对象
   */
  BaseWeapon.prototype.setFuZu = function (fuzu) {
    // 预留方法，子类实现
    console.log("BaseWeapon: setFuZu 方法需要在子类中实现");
  };
  
  /**
   * 清理所有符咒效果
   */
  BaseWeapon.prototype.cleanAllFuZu = function () {
    // 预留方法，子类实现
    console.log("BaseWeapon: cleanAllFuZu 方法需要在子类中实现");
  };
  
  /**
   * 武器初始化方法
   * @param {Object} weaponData - 武器数据
   */
  BaseWeapon.prototype.init = function (weaponData) {
    // 存储武器数据
    this._weapondata = weaponData;

    // 初始化基础属性
    this.currentHp = 100;
    this.maxHp = 100;
    this.changhp = 0;
    this.isrun = true; // 设置为运行状态
    this.bulletids = []; // 初始化子弹数组

    // 如果有武器数据，尝试添加默认子弹
    if (weaponData && weaponData.id) {
      // 添加一个默认子弹类型
      this.addBullet(1.0, 1, 1.0); // CD时间1秒，子弹ID为1，最大时间1秒
    }

    console.log("BaseWeapon: 初始化武器", weaponData);
  };

  /**
   * 初始化武器盒子/容器
   */
  BaseWeapon.prototype.initBox = function () {
    // 初始化武器的显示容器
    console.log("BaseWeapon: 初始化武器盒子");

    // 设置基础状态
    if (this.node) {
      this.node.active = true;
      this.node.opacity = 255;
    }

    // 初始化动画节点位置
    if (this.aniNode && this.aniNode.length > 0) {
      for (let i = 0; i < this.aniNode.length; i++) {
        const node = this.aniNode[i];
        if (node) {
          this.aniNodeStartPos[i] = node.getPosition();
        }
      }
    }
  };

  /**
   * 获取武器血量比例
   * @returns {number} 血量比例 (0-1)
   */
  BaseWeapon.prototype.getHpPro = function () {
    if (this.maxHp <= 0) {
      return 0;
    }
    return this.currentHp / this.maxHp;
  };

  /**
   * 获取武器当前血量
   * @returns {number} 当前血量
   */
  BaseWeapon.prototype.getHp = function () {
    return this.currentHp || 100;
  };

  /**
   * 设置武器血量
   * @param {number} hp - 血量值
   */
  BaseWeapon.prototype.setHp = function (hp) {
    this.currentHp = Math.max(0, Math.min(hp, this.maxHp));
  };

  /**
   * 获取武器最大血量
   * @returns {number} 最大血量
   */
  BaseWeapon.prototype.getMaxHp = function () {
    return this.maxHp || 100;
  };

  /**
   * 设置武器最大血量
   * @param {number} maxHp - 最大血量值
   */
  BaseWeapon.prototype.setMaxHp = function (maxHp) {
    this.maxHp = Math.max(1, maxHp);
    // 确保当前血量不超过最大血量
    if (this.currentHp > this.maxHp) {
      this.currentHp = this.maxHp;
    }
  };

  /**
   * 延迟定位方法
   * @param {cc.Vec2} position - 目标位置
   * @param {number} angle - 目标角度
   * @param {number} deltaTime - 时间间隔
   */
  BaseWeapon.prototype.delayLocation = function (position, angle, deltaTime) {
    if (!this.node || !position) {
      return;
    }

    // 简单的位置插值移动
    const currentPos = this.node.getPosition();
    const distance = cc.Vec2.distance(currentPos, position);

    if (distance > 5) {
      // 如果距离较远，进行插值移动
      const moveSpeed = 200; // 移动速度
      const maxMove = moveSpeed * deltaTime;
      const moveDistance = Math.min(maxMove, distance);

      const direction = cc.Vec2.subtract(cc.v2(), position, currentPos).normalize();
      const newPos = cc.Vec2.add(cc.v2(), currentPos, direction.mul(moveDistance));

      this.node.setPosition(newPos);
    } else {
      // 距离很近，直接设置位置
      this.node.setPosition(position);
    }

    // 设置角度
    if (typeof angle === 'number') {
      this.node.angle = angle;
    }
  };
  
  /**
   * 武器更新方法
   * @param {number} deltaTime - 帧间隔时间
   */
  BaseWeapon.prototype.onUpdate = function (deltaTime) {
    // 如果武器没有运行，直接返回
    if (!this.isrun) {
      return;
    }

    // 初始化子弹数组
    if (!this.bulletids) {
      this.bulletids = [];
    }

    // 更新所有子弹的CD时间
    for (let i = 0; i < this.bulletids.length; i++) {
      const bulletInfo = this.bulletids[i];
      bulletInfo.cdtime -= deltaTime;

      // 如果CD时间到了，尝试发射子弹
      if (bulletInfo.cdtime <= 0) {
        const sendResult = this.sendBullet(bulletInfo.bulletid);

        // 根据发射结果重置CD时间
        if (sendResult === 1) {
          // 发射失败（超出范围等），短时间后重试
          bulletInfo.cdtime = 0.1;
        } else {
          // 发射成功或其他情况，使用正常CD时间
          bulletInfo.cdtime = this.getAttackTime(bulletInfo.maxtime);
        }
      }

      this.bulletids[i] = bulletInfo;
    }
  };

  /**
   * 添加子弹类型
   * @param {number} cdTime - CD时间
   * @param {number} bulletId - 子弹ID
   * @param {number} maxTime - 最大时间
   */
  BaseWeapon.prototype.addBullet = function (cdTime, bulletId, maxTime) {
    if (!this.bulletids) {
      this.bulletids = [];
    }

    this.bulletids.push({
      cdtime: cdTime,
      bulletid: bulletId,
      maxtime: maxTime
    });
  };

  /**
   * 发射子弹
   * @param {number} bulletId - 子弹ID
   * @returns {number} 发射结果 (1=失败, 2=成功, 3=特殊成功)
   */
  BaseWeapon.prototype.sendBullet = function (bulletId) {
    // 查找最近的敌人目标
    const target = this.findNearestTarget();

    if (!target) {
      return 2; // 没有目标
    }

    // 检查距离
    const distance = this.getDistanceToTarget(target);
    if (distance > this.getAttackRange()) {
      return 1; // 超出攻击范围
    }

    // 设置锁定目标
    this.setLockNode(target);

    // 创建子弹
    if (bulletId && this.createBullet) {
      try {
        this.createBullet(bulletId, target);
        return 3; // 成功创建子弹
      } catch (error) {
        console.warn("BaseWeapon: 创建子弹失败", error);
        return 2;
      }
    }

    return 2;
  };

  /**
   * 查找最近的目标
   * @returns {cc.Node} 最近的敌人节点
   */
  BaseWeapon.prototype.findNearestTarget = function () {
    // 这里需要调用游戏逻辑来查找最近的敌人
    // 暂时返回null，子类可以重写
    if (typeof $9Logic_Game !== 'undefined' && $9Logic_Game.default && $9Logic_Game.default.getLatelyMoster) {
      const worldPos = this.node.convertToWorldSpaceAR(cc.v2());
      return $9Logic_Game.default.getLatelyMoster(worldPos);
    }
    return null;
  };

  /**
   * 获取到目标的距离
   * @param {cc.Node} target - 目标节点
   * @returns {number} 距离
   */
  BaseWeapon.prototype.getDistanceToTarget = function (target) {
    if (!target || !this.node) {
      return Infinity;
    }

    const myPos = this.node.convertToWorldSpaceAR(cc.v2());
    const targetPos = target.convertToWorldSpaceAR(cc.v2());
    return cc.Vec2.distance(myPos, targetPos);
  };

  /**
   * 获取攻击范围
   * @returns {number} 攻击范围
   */
  BaseWeapon.prototype.getAttackRange = function () {
    // 默认攻击范围，子类可以重写
    return 1100;
  };

  /**
   * 获取攻击时间
   * @param {number} maxTime - 最大时间
   * @returns {number} 攻击时间
   */
  BaseWeapon.prototype.getAttackTime = function (maxTime) {
    // 默认返回最大时间，子类可以重写
    return maxTime || 1.0;
  };

  /**
   * 设置锁定节点
   * @param {cc.Node} target - 目标节点
   */
  BaseWeapon.prototype.setLockNode = function (target) {
    this.lockTarget = target;
  };

  /**
   * 创建子弹（基础实现）
   * @param {number} bulletId - 子弹ID
   * @param {cc.Node} target - 目标节点
   */
  BaseWeapon.prototype.createBullet = function (bulletId, target) {
    // 尝试使用游戏的子弹创建系统
    try {
      // 检查是否有子弹配置数据
      if (typeof $9Excel !== 'undefined' && $9Excel.Excel && $9Excel.Excel.haizhanbullet) {
        const bulletConfig = $9Excel.Excel.haizhanbullet(bulletId);
        if (bulletConfig) {
          // 尝试使用子弹工厂创建子弹
          if (typeof $9BulletFactory !== 'undefined' && $9BulletFactory.default) {
            this.createBulletWithFactory(bulletConfig, target);
            return;
          }

          // 尝试使用子弹创建器
          if (typeof $9Logic_Game !== 'undefined' && $9Logic_Game.default && $9Logic_Game.default.bulletNode) {
            const bulletCreator = $9Logic_Game.default.bulletNode.getComponent('BulletCreate');
            if (bulletCreator && bulletCreator.createBullet) {
              bulletCreator.createBullet(bulletConfig.pfbname, this, this.node, target);
              console.log("BaseWeapon: 成功创建子弹", bulletId);
              return;
            }
          }
        }
      }

      // 如果上述方法都不可用，使用简单的视觉效果
      this.createSimpleBulletEffect(target);

    } catch (error) {
      console.warn("BaseWeapon: 创建子弹时出错", error);
      // 降级到简单效果
      this.createSimpleBulletEffect(target);
    }
  };

  /**
   * 使用工厂创建子弹
   * @param {Object} bulletConfig - 子弹配置
   * @param {cc.Node} target - 目标节点
   */
  BaseWeapon.prototype.createBulletWithFactory = function (bulletConfig, target) {
    $9BulletFactory.default.getBullet(bulletConfig.id).then((bulletNode) => {
      if (bulletNode) {
        const bulletComponent = bulletNode.getComponent($9BaseBullet.default);
        if (bulletComponent) {
          const bulletParams = {
            id: bulletConfig.id,
            createNode: this,
            from: this.node.getPosition(),
            target: target,
            callback: this.onBulletHit.bind(this)
          };
          bulletComponent.init(bulletParams);
          console.log("BaseWeapon: 工厂创建子弹成功", bulletConfig.id);
        }
      }
    }).catch((error) => {
      console.warn("BaseWeapon: 工厂创建子弹失败", error);
    });
  };

  /**
   * 创建简单的子弹视觉效果
   * @param {cc.Node} target - 目标节点
   */
  BaseWeapon.prototype.createSimpleBulletEffect = function (target) {
    if (!this.node || !target) {
      return;
    }

    // 创建一个简单的子弹节点
    const bulletNode = new cc.Node("SimpleBullet");
    const sprite = bulletNode.addComponent(cc.Sprite);

    // 设置子弹外观（简单的白色圆点）
    const texture = new cc.Texture2D();
    sprite.spriteFrame = new cc.SpriteFrame(texture);
    bulletNode.width = 10;
    bulletNode.height = 10;
    bulletNode.color = cc.Color.YELLOW;

    // 设置初始位置
    bulletNode.setPosition(this.node.getPosition());

    // 添加到场景
    if (this.node.parent) {
      this.node.parent.addChild(bulletNode);
    }

    // 计算目标位置
    const targetPos = target.getPosition ? target.getPosition() : target;

    // 创建移动动画
    const moveAction = cc.moveTo(0.5, targetPos);
    const fadeAction = cc.fadeOut(0.5);
    const removeAction = cc.callFunc(() => {
      bulletNode.removeFromParent();
      this.onBulletHit(target);
    });

    const sequence = cc.sequence(cc.spawn(moveAction, fadeAction), removeAction);
    bulletNode.runAction(sequence);

    console.log("BaseWeapon: 创建简单子弹效果");
  };

  /**
   * 子弹命中回调
   * @param {cc.Node} target - 命中的目标
   */
  BaseWeapon.prototype.onBulletHit = function (target) {
    // 子类可以重写这个方法来处理命中逻辑
    console.log("BaseWeapon: 子弹命中目标", target);
  };

  /**
   * 检查武器是否已死亡/销毁
   * @returns {boolean} 是否已死亡
   */
  BaseWeapon.prototype.isDie = function () {
    // 检查节点是否有效
    if (!this.node || !this.node.isValid) {
      return true;
    }

    // 检查血量是否为0
    if (this.currentHp <= 0) {
      return true;
    }

    // 检查是否处于运行状态
    if (!this.isrun) {
      return false; // 不运行不等于死亡
    }

    return false;
  };

  /**
   * 武器死亡处理
   */
  BaseWeapon.prototype.die = function () {
    this.currentHp = 0;
    this.isrun = false;

    // 停止所有动画和定时器
    this.stopAni();

    // 清理子弹数组
    if (this.bulletids) {
      this.bulletids = [];
    }

    // 触发死亡事件
    if (this.node && this.node.isValid) {
      this.node.active = false;
    }

    console.log("BaseWeapon: 武器已死亡");
  };

  /**
   * 复活武器
   */
  BaseWeapon.prototype.revive = function () {
    this.currentHp = this.maxHp;
    this.isrun = true;

    if (this.node && this.node.isValid) {
      this.node.active = true;
    }

    console.log("BaseWeapon: 武器已复活");
  };

  /**
   * 播放武器浮动动画
   * @param {number} speed - 动画速度倍数，默认为1
   */
  BaseWeapon.prototype.playerAni = function (speed) {
    const self = this;
    if (speed === undefined) {
      speed = 1;
    }

    // 停止之前的动画
    this.stopAni();

    // 开始循环动画
    function startAnimation() {
      // 生成随机偏移量
      const offsetX = Math.floor(Math.random() * 7) + 4; // 4-10的随机数
      const offsetY = Math.floor(Math.random() * 7) + 4; // 4-10的随机数

      // 随机决定方向
      const finalOffsetX = Math.random() >= 0.5 ? offsetX : -offsetX;
      const finalOffsetY = Math.random() >= 0.5 ? offsetY : -offsetY;

      // 如果有动画节点，对第一个子节点执行动画
      if (self.node && self.node.children && self.node.children.length > 0) {
        const targetNode = self.node.children[0];

        cc.tween(targetNode)
          .to(1 * speed, {
            x: finalOffsetX,
            y: finalOffsetY
          })
          .to(1 * speed, {
            x: 0,
            y: 0
          })
          .call(function () {
            // 动画完成后的回调
          })
          .start();
      }

      // 安排下一次动画
      self.scheduleOnce(function () {
        startAnimation();
      }, 2 * speed);
    }

    // 开始动画循环
    startAnimation();
  };

  /**
   * 停止武器浮动动画
   */
  BaseWeapon.prototype.stopAni = function () {
    // 停止所有缓动动画
    if (this.node && this.node.children && this.node.children.length > 0) {
      const targetNode = this.node.children[0];
      cc.Tween.stopAllByTarget(targetNode);
      // 重置位置
      targetNode.setPosition(0, 0);
    }

    // 取消所有定时器
    this.unscheduleAllCallbacks();
  };

  /**
   * 重置武器显示
   * @param {number} weaponId - 武器ID
   * @param {number} weaponLevel - 武器等级
   */
  BaseWeapon.prototype.ResetShow = function (weaponId, weaponLevel) {
    // 存储武器数据
    this.currentWeaponId = weaponId;
    this.currentWeaponLevel = weaponLevel;

    // 预留方法，子类可以重写实现具体的显示逻辑
    console.log("BaseWeapon: ResetShow 武器ID=" + weaponId + ", 等级=" + weaponLevel);

    // 基础的重置逻辑
    if (this.node) {
      // 重置节点状态
      this.node.stopAllActions();
      this.node.setPosition(0, 0);
      this.node.angle = 0;
      this.node.scale = 1;
      this.node.opacity = 255;
    }
  };

  /**
   * 改变武器移动时间状态
   * @param {Object} moveData - 移动数据 {isMove: boolean, isStart: boolean}
   */
  BaseWeapon.prototype.ChangeIsMoveTime = function (moveData) {
    if (!moveData) {
      console.warn("BaseWeapon: ChangeIsMoveTime 参数为空");
      return;
    }

    const isMove = moveData.isMove;
    const isStart = moveData.isStart;

    // 存储移动状态
    this.isMoveTime = isMove;
    this.isMoveStart = isStart;

    console.log("BaseWeapon: ChangeIsMoveTime 移动=" + isMove + ", 开始=" + isStart);

    // 处理移动浪花效果
    if (this.moveLang && this.moveLang.children && this.moveLang.children.length > 0) {
      const langEffect = this.moveLang.children[0];
      if (isMove && isStart) {
        // 开始移动，显示移动浪花
        this.moveLang.active = true;
        if (langEffect) {
          langEffect.active = true;
        }
      } else if (!isMove) {
        // 停止移动，隐藏移动浪花
        this.moveLang.active = false;
        if (langEffect) {
          langEffect.active = false;
        }
      }
    }

    // 处理停止浪花效果
    if (this.stopLang && this.stopLang.children && this.stopLang.children.length > 0) {
      const stopEffect = this.stopLang.children[0];
      if (!isMove && isStart) {
        // 停止移动，显示停止浪花
        this.stopLang.active = true;
        if (stopEffect) {
          stopEffect.active = true;
        }
      } else if (isMove) {
        // 开始移动，隐藏停止浪花
        this.stopLang.active = false;
        if (stopEffect) {
          stopEffect.active = false;
        }
      }
    }
  };

  // ==================== 属性访问器 ====================

  /**
   * 武器数据属性访问器
   */
  Object.defineProperty(BaseWeapon.prototype, "weapondata", {
    get: function () {
      return this._weapondata;
    },
    set: function (value) {
      this._weapondata = value;
    },
    enumerable: false,
    configurable: true
  });

  // 应用Cocos Creator装饰器
  cc_decorate([property({
    type: cc.Node
  })], BaseWeapon.prototype, "aniNode", undefined);
  
  cc_decorate([property([cc.Sprite])], BaseWeapon.prototype, "shipSpriteArray", undefined);
  
  cc_decorate([property([sp.Skeleton])], BaseWeapon.prototype, "shipSpineArray", undefined);
  
  cc_decorate([property(cc.Node)], BaseWeapon.prototype, "node_sendpos", undefined);
  
  cc_decorate([property(cc.Node)], BaseWeapon.prototype, "moveLang", undefined);
  
  cc_decorate([property(cc.Node)], BaseWeapon.prototype, "stopLang", undefined);
  
  return cc_decorate([
    ccclass, 
    inspector("packages://base-ui/dist/inspector.js")
  ], BaseWeapon);
  
}($9Object.cObject);

// 导出武器基类
exports.default = BaseWeaponClass;
